cmake_minimum_required(VERSION 3.1...3.25)
project(
	LearnWebGPU
	VERSION 0.1.0
	LANGUAGES CXX C
)

include(utils.cmake)

# We add an option to enable different settings when developping the app than
# when distributing it.
option(DEV_MODE "Set up development helper settings" ON)

# WebAssembly specific settings
if(EMSCRIPTEN)
	set(CMAKE_CXX_STANDARD 20)
	set(CMAKE_CXX_STANDARD_REQUIRED ON)
	set(CMAKE_CXX_EXTENSIONS OFF)

	# Emscripten specific compile flags
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s USE_WEBGPU=1")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s USE_GLFW=3")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s WASM=1")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s ALLOW_MEMORY_GROWTH=1")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s NO_EXIT_RUNTIME=0")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s ASSERTIONS=1")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s DISABLE_EXCEPTION_CATCHING=0")

	# Enable WebGPU debugging
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s WEBGPU_DEBUG=1")

	# Set output directory for WebAssembly build
	set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/redist_wasm)
endif()

add_subdirectory(webgpu)
add_subdirectory(imgui)

if(EMSCRIPTEN)
	# For WebAssembly, we don't need glfw or glfw3webgpu
else()
	add_subdirectory(glfw)
	add_subdirectory(glfw3webgpu)
endif()

add_executable(App
	main.cpp
	Application.h
	Application.cpp
	ResourceManager.h
	ResourceManager.cpp
	implementations.cpp
)

if(EMSCRIPTEN)
	# For WebAssembly, embed resources directly
	target_compile_definitions(App PRIVATE
		RESOURCE_DIR="./resources"
	)
	# Preload resource files for better performance (use preload instead of embed for larger files)
	set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} --preload-file ${CMAKE_CURRENT_SOURCE_DIR}/resources@resources")
elseif(DEV_MODE)
	# In dev mode, we load resources from the source tree, so that when we
	# dynamically edit resources (like shaders), these are correctly
	# versionned.
	target_compile_definitions(App PRIVATE
		RESOURCE_DIR="${CMAKE_CURRENT_SOURCE_DIR}/resources"
	)
else()
	# In release mode, we just load resources relatively to wherever the
	# executable is launched from, so that the binary is portable
	target_compile_definitions(App PRIVATE
		RESOURCE_DIR="./resources"
	)
endif()

target_include_directories(App PRIVATE .)

if(EMSCRIPTEN)
	target_link_libraries(App PRIVATE webgpu imgui)
else()
	target_link_libraries(App PRIVATE glfw webgpu glfw3webgpu imgui)
endif()

if(NOT EMSCRIPTEN)
	set_target_properties(App PROPERTIES
		CXX_STANDARD 17
		CXX_STANDARD_REQUIRED ON
		CXX_EXTENSIONS OFF
		VS_DEBUGGER_ENVIRONMENT "DAWN_DEBUG_BREAK_ON_ERROR=1"
	)
	target_treat_all_warnings_as_errors(App)
	target_copy_webgpu_binaries(App)
else()
	set_target_properties(App PROPERTIES
		CXX_STANDARD 20
		CXX_STANDARD_REQUIRED ON
		CXX_EXTENSIONS OFF
		SUFFIX ".html"
	)
	# WebAssembly specific linker flags
	set_target_properties(App PROPERTIES
		LINK_FLAGS "-s USE_WEBGPU=1 -s USE_GLFW=3 -s WASM=1 -s ALLOW_MEMORY_GROWTH=1 -s NO_EXIT_RUNTIME=0 -s ASSERTIONS=1 -s DISABLE_EXCEPTION_CATCHING=0 --shell-file ${CMAKE_CURRENT_SOURCE_DIR}/shell.html"
	)
endif()

if (MSVC)
	# Ignore a warning that GLM requires to bypass
	# Disable warning C4201: nonstandard extension used: nameless struct/union
	target_compile_options(App PUBLIC /wd4201)
	# Disable warning C4305: truncation from 'int' to 'bool' in 'if' condition
	target_compile_options(App PUBLIC /wd4305)

	# Ignore a warning that stb_image requires to bypass
	# Disable warning C4244: conversion from 'int' to 'short', possible loss of data
	target_compile_options(App PUBLIC /wd4244)
endif (MSVC)
