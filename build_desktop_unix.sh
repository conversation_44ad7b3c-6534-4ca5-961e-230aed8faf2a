#!/bin/bash

echo "Building Desktop version for Linux/macOS..."

# Create build directory
mkdir -p build_desktop
cd build_desktop

# Choose WebGPU backend
echo ""
echo "========================================"
echo "Choose WebGPU Backend:"
echo "1. WGPU (Rust-based, default)"
echo "2. DAWN (Chrome-based)"
echo "========================================"
read -p "Enter your choice (1-2): " choice

if [ "$choice" = "2" ]; then
    BACKEND="DAWN"
    echo "Using Dawn backend..."
else
    BACKEND="WGPU"
    echo "Using wgpu-native backend..."
fi

# Choose build type
echo ""
echo "========================================"
echo "Choose Build Type:"
echo "1. Release (default)"
echo "2. Debug"
echo "========================================"
read -p "Enter your choice (1-2): " build_choice

if [ "$build_choice" = "2" ]; then
    BUILD_TYPE="Debug"
    echo "Using Debug build..."
else
    BUILD_TYPE="Release"
    echo "Using Release build..."
fi

# Configure CMake
echo ""
echo "Configuring CMake..."
cmake .. -DCMAKE_BUILD_TYPE=$BUILD_TYPE -DWEBGPU_BACKEND=$BACKEND -DWEBGPU_BUILD_FROM_SOURCE=OFF

if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    cd ..
    exit 1
fi

# Build the project
echo ""
echo "Building project..."
cmake --build . --config $BUILD_TYPE -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)

if [ $? -ne 0 ]; then
    echo "Build failed!"
    cd ..
    exit 1
fi

# Create redist directory
mkdir -p ../redist_desktop

# Copy built files to redist directory
echo ""
echo "Copying files to redist_desktop..."
cp App ../redist_desktop/ 2>/dev/null || cp $BUILD_TYPE/App ../redist_desktop/ 2>/dev/null

# Copy shared libraries
find . -name "*.so" -exec cp {} ../redist_desktop/ \; 2>/dev/null
find . -name "*.dylib" -exec cp {} ../redist_desktop/ \; 2>/dev/null

# Copy resources
cp -r ../resources ../redist_desktop/ 2>/dev/null

cd ..

echo ""
echo "========================================"
echo "Desktop build completed successfully!"
echo "Backend: $BACKEND"
echo "Build Type: $BUILD_TYPE"
echo "Files are in the redist_desktop directory."
echo "Run ./redist_desktop/App to test."
echo "========================================"
