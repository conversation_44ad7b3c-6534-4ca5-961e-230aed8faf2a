@echo off
echo LearnWebGPU Multi-Platform Build System
echo ========================================

echo.
echo Choose build target:
echo 1. Desktop (Windows)
echo 2. WebAssembly
echo 3. Both Desktop and WebAssembly
echo ========================================
set /p choice="Enter your choice (1-3): "

if "%choice%"=="1" (
    echo.
    echo Building Desktop version...
    call build_desktop_windows.bat
) else if "%choice%"=="2" (
    echo.
    echo Building WebAssembly version...
    call build_wasm.bat
) else if "%choice%"=="3" (
    echo.
    echo Building Desktop version...
    call build_desktop_windows.bat
    echo.
    echo Building WebAssembly version...
    call build_wasm.bat
) else (
    echo Invalid choice!
    pause
    exit /b 1
)

echo.
echo ========================================
echo All builds completed!
echo ========================================
