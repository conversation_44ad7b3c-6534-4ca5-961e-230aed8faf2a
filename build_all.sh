#!/bin/bash

echo "LearnWebGPU Multi-Platform Build System"
echo "========================================"

echo ""
echo "Choose build target:"
echo "1. Desktop (Linux/macOS)"
echo "2. WebAssembly"
echo "3. Both Desktop and WebAssembly"
echo "========================================"
read -p "Enter your choice (1-3): " choice

if [ "$choice" = "1" ]; then
    echo ""
    echo "Building Desktop version..."
    ./build_desktop_unix.sh
elif [ "$choice" = "2" ]; then
    echo ""
    echo "Building WebAssembly version..."
    ./build_wasm_unix.sh
elif [ "$choice" = "3" ]; then
    echo ""
    echo "Building Desktop version..."
    ./build_desktop_unix.sh
    echo ""
    echo "Building WebAssembly version..."
    ./build_wasm_unix.sh
else
    echo "Invalid choice!"
    exit 1
fi

echo ""
echo "========================================"
echo "All builds completed!"
echo "========================================"
