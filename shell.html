<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LearnWebGPU - WebAssembly Demo</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #4CAF50;
            margin-bottom: 20px;
        }
        
        .info {
            background-color: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .canvas-container {
            text-align: center;
            background-color: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        canvas {
            border: 2px solid #4CAF50;
            border-radius: 4px;
            background-color: #000000;
        }
        
        .controls {
            background-color: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .status {
            background-color: #2a2a2a;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .error {
            color: #ff6b6b;
        }
        
        .success {
            color: #4CAF50;
        }
        
        .warning {
            color: #ffa726;
        }
        
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background-color: #45a049;
        }
        
        button:disabled {
            background-color: #666;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>LearnWebGPU - WebAssembly Demo</h1>
        
        <div class="info">
            <h3>WebGPU 3D Rendering Demo</h3>
            <p>This demo showcases WebGPU rendering capabilities using WebAssembly compiled from C++.</p>
            <p><strong>Features:</strong></p>
            <ul>
                <li>3D mesh rendering with textures</li>
                <li>Real-time lighting calculations</li>
                <li>Interactive camera controls (mouse drag to rotate, scroll to zoom)</li>
                <li>ImGui interface for parameter adjustment</li>
            </ul>
        </div>
        
        <div class="canvas-container">
            <canvas id="canvas" width="800" height="600"></canvas>
        </div>
        
        <div class="controls">
            <h3>Controls</h3>
            <p><strong>Mouse:</strong> Drag to rotate camera, scroll to zoom</p>
            <p><strong>GUI:</strong> Use the ImGui interface in the canvas for lighting controls</p>
            <button id="fullscreen-btn">Toggle Fullscreen</button>
            <button id="reset-camera-btn">Reset Camera</button>
        </div>
        
        <div class="status" id="status">
            <div class="success">Initializing WebAssembly module...</div>
        </div>
    </div>

    <script>
        // WebGPU support check
        async function checkWebGPUSupport() {
            if (!navigator.gpu) {
                addStatus('WebGPU is not supported in this browser.', 'error');
                return false;
            }
            
            try {
                const adapter = await navigator.gpu.requestAdapter();
                if (!adapter) {
                    addStatus('No WebGPU adapter found.', 'error');
                    return false;
                }
                
                const device = await adapter.requestDevice();
                addStatus('WebGPU is supported and available!', 'success');
                return true;
            } catch (error) {
                addStatus('WebGPU initialization failed: ' + error.message, 'error');
                return false;
            }
        }
        
        function addStatus(message, type = 'info') {
            const status = document.getElementById('status');
            const div = document.createElement('div');
            div.className = type;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            status.appendChild(div);
            status.scrollTop = status.scrollHeight;
        }
        
        // Canvas and controls setup
        const canvas = document.getElementById('canvas');
        const fullscreenBtn = document.getElementById('fullscreen-btn');
        const resetCameraBtn = document.getElementById('reset-camera-btn');
        
        fullscreenBtn.addEventListener('click', () => {
            if (canvas.requestFullscreen) {
                canvas.requestFullscreen();
            }
        });
        
        // Module configuration
        var Module = {
            canvas: canvas,
            preRun: [function() {
                addStatus('WebAssembly module pre-run...', 'info');
            }],
            postRun: [function() {
                addStatus('WebAssembly module loaded successfully!', 'success');
                addStatus('Application started. Use mouse to interact with the 3D scene.', 'info');
            }],
            print: function(text) {
                addStatus('App: ' + text, 'info');
            },
            printErr: function(text) {
                addStatus('Error: ' + text, 'error');
            },
            onAbort: function(what) {
                addStatus('Application aborted: ' + what, 'error');
            },
            onRuntimeInitialized: function() {
                addStatus('WebAssembly runtime initialized', 'success');
            }
        };
        
        // Check WebGPU support before loading the module
        checkWebGPUSupport().then(supported => {
            if (supported) {
                addStatus('Loading WebAssembly module...', 'info');
            } else {
                addStatus('Cannot proceed without WebGPU support.', 'error');
                canvas.style.display = 'none';
            }
        });
    </script>
    
    {{{ SCRIPT }}}
</body>
</html>
