# LearnWebGPU - Multi-Platform WebGPU Application

This is a modern WebGPU-based 3D rendering application that demonstrates cross-platform graphics programming using C++. The project showcases advanced WebGPU features including 3D mesh rendering, texture mapping, real-time lighting, and interactive camera controls.

## Features

- **Cross-Platform Rendering**: Supports Windows, Linux, macOS (desktop) and Web (WebAssembly)
- **Modern WebGPU API**: Uses the latest WebGPU standard for high-performance graphics
- **Multiple Backend Support**: 
  - Desktop: wgpu-native (Rust-based) or Dawn (Chrome-based)
  - Web: Browser WebGPU or emdawnwebgpu
- **3D Graphics**: OBJ model loading, texture mapping, real-time lighting
- **Interactive Controls**: Mouse camera controls, ImGui parameter adjustment
- **Automated Build System**: One-click builds for all platforms

## Quick Start

### Prerequisites

- **CMake 3.14+**
- **C++17 compiler** (MSVC, GCC, or Clang)
- **Git** (for dependency fetching)
- **Emscripten** (for WebAssembly builds)

### Building

#### Option 1: Automated Build Scripts

**Windows:**
```cmd
# Build desktop version
build_desktop_windows.bat

# Build WebAssembly version  
build_wasm.bat

# Build both
build_all.bat
```

**Linux/macOS:**
```bash
# Build desktop version
./build_desktop_unix.sh

# Build WebAssembly version
./build_wasm_unix.sh

# Build both
./build_all.sh
```

#### Option 2: Manual CMake

**Desktop Build:**
```bash
mkdir build_desktop
cd build_desktop
cmake .. -DCMAKE_BUILD_TYPE=Release -DWEBGPU_BACKEND=WGPU
cmake --build . --config Release
```

**WebAssembly Build:**
```bash
# Setup Emscripten environment first
source /path/to/emsdk/emsdk_env.sh  # Linux/macOS
# or
call C:\dev\emsdk\emsdk_env.bat     # Windows

mkdir build_wasm
cd build_wasm
emcmake cmake .. -DCMAKE_BUILD_TYPE=Release -DWEBGPU_BACKEND=EMDAWNWEBGPU
emmake make
```

## WebGPU Backend Options

### Desktop Platforms
- **WGPU** (default): Rust-based implementation, stable and well-tested
- **DAWN**: Chrome-based implementation, cutting-edge features

### WebAssembly Platforms
- **EMDAWNWEBGPU** (default): More up-to-date WebGPU port
- **EMSCRIPTEN**: Built-in emscripten WebGPU support

## Project Structure

```
LearnWebGPU/
├── Application.h/cpp          # Main application class
├── ResourceManager.h/cpp      # Resource loading and management
├── main.cpp                   # Application entry point
├── resources/                 # 3D models, textures, shaders
├── build_*.bat/sh            # Automated build scripts
├── shell.html                # WebAssembly HTML template
└── redist_*/                 # Build output directories
```

## Dependencies

All dependencies are automatically managed via CMake FetchContent:

- **[WebGPU-distribution](https://github.com/eliemichel/WebGPU-distribution)**: Unified WebGPU implementation
- **[GLFW](https://github.com/glfw/glfw)**: Cross-platform windowing (desktop only)
- **[glfw3webgpu](https://github.com/eliemichel/glfw3webgpu)**: GLFW-WebGPU integration (desktop only)
- **[GLM](https://github.com/g-truc/glm)**: Mathematics library
- **[Dear ImGui](https://github.com/ocornut/imgui)**: Immediate mode GUI
- **stb_image**: Image loading (header-only)
- **tiny_obj_loader**: OBJ model loading (header-only)

## Browser Compatibility (WebAssembly)

- **Chrome/Edge**: Version 113+ (enable WebGPU experimental features)
- **Firefox**: Version 113+ (enable WebGPU in about:config)
- **Safari**: Version 16.4+ (WebGPU enabled by default)

## Controls

- **Mouse Drag**: Rotate camera around the 3D model
- **Mouse Scroll**: Zoom in/out
- **ImGui Interface**: Adjust lighting parameters and rendering settings

## Output Directories

- **redist_desktop/**: Desktop application and dependencies
- **redist_wasm/**: WebAssembly files for web deployment

## Troubleshooting

### Desktop Build Issues
- Ensure CMake 3.14+ is installed
- Check that your compiler supports C++17
- Verify internet connection for dependency downloading

### WebAssembly Build Issues
- Ensure Emscripten is properly installed and activated
- Check that the browser supports WebGPU
- Verify that the web server serves .wasm files with correct MIME type

### Performance Issues
- Try different WebGPU backends
- Check GPU driver compatibility
- Monitor browser console for WebGPU errors

## License

MIT License - see LICENSE file for details

## Contributing

This project is based on the [Learn WebGPU for C++](https://eliemichel.github.io/LearnWebGPU) tutorial series. Contributions are welcome!

## Related Projects

- [Learn WebGPU](https://eliemichel.github.io/LearnWebGPU) - Tutorial series
- [WebGPU-Cpp](https://github.com/eliemichel/WebGPU-Cpp) - C++ bindings
- [WebGPU-distribution](https://github.com/eliemichel/WebGPU-distribution) - Unified WebGPU distribution
