/**
 * This file is part of the "Learn WebGPU for C++" book.
 *   https://github.com/eliemichel/LearnWebGPU
 *
 * MIT License
 * Copyright (c) 2022-2024 <PERSON><PERSON>
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 */

/**
 * Exactly one of your source files must #define WEBGPU_CPP_IMPLEMENTATION
 * before including this header.
 * 
 * NB: This file has been generated by the webgpu-cpp generator
 *   (see https://github.com/eliemichel/webgpu-cpp )
 */

#pragma once

#include <webgpu/webgpu.h>
#include <webgpu/wgpu.h>

#include <iostream>
#include <vector>
#include <functional>
#include <cassert>
#include <memory>

#if __EMSCRIPTEN__
#include <emscripten.h>
#endif

#ifdef _MSVC_LANG
#  if _MSVC_LANG >= 202002L
#   define NO_DISCARD [[nodiscard("You should keep this handle alive for as long as the callback may get invoked.")]]
#  elif _MSVC_LANG >= 201703L
#   define NO_DISCARD [[nodiscard]]
#  else
#   define NO_DISCARD
#  endif
#else
#  if __cplusplus >= 202002L
#    define NO_DISCARD [[nodiscard("You should keep this handle alive for as long as the callback may get invoked.")]]
#  elif __cplusplus >= 201703L
#    define NO_DISCARD [[nodiscard]]
#  else
#    define NO_DISCARD
#  endif
#endif

/**
 * A namespace providing a more C++ idiomatic API to WebGPU.
 */
namespace wgpu {

struct DefaultFlag {};
constexpr DefaultFlag Default;

#define HANDLE(Type) \
class Type { \
public: \
	typedef Type S; /* S == Self */ \
	typedef WGPU ## Type W; /* W == WGPU Type */ \
	Type() : m_raw(nullptr) {} \
	Type(const W& w) : m_raw(w) {} \
	operator W&() { return m_raw; } \
	operator const W&() const { return m_raw; } \
	operator bool() const { return m_raw != nullptr; } \
	bool operator==(const Type& other) const { return m_raw == other.m_raw; } \
	bool operator!=(const Type& other) const { return m_raw != other.m_raw; } \
	bool operator==(const W& other) const { return m_raw == other; } \
	bool operator!=(const W& other) const { return m_raw != other; } \
	friend auto operator<<(std::ostream &stream, const S& self) -> std::ostream & { \
		return stream << "<wgpu::" << #Type << " " << self.m_raw << ">"; \
	} \
private: \
	W m_raw; \
public:

#define DESCRIPTOR(Type) \
struct Type : public WGPU ## Type { \
public: \
	typedef Type S; /* S == Self */ \
	typedef WGPU ## Type W; /* W == WGPU Type */ \
	Type() : W() { nextInChain = nullptr; } \
	Type(const W &other) : W(other) { nextInChain = nullptr; } \
	Type(const DefaultFlag &) : W() { setDefault(); } \
	Type& operator=(const DefaultFlag &) { setDefault(); return *this; } \
	friend auto operator<<(std::ostream &stream, const S&) -> std::ostream & { \
		return stream << "<wgpu::" << #Type << ">"; \
	} \
public:

#define STRUCT_NO_OSTREAM(Type) \
struct Type : public WGPU ## Type { \
public: \
	typedef Type S; /* S == Self */ \
	typedef WGPU ## Type W; /* W == WGPU Type */ \
	Type() : W() {} \
	Type(const W &other) : W(other) {} \
	Type(const DefaultFlag &) : W() { setDefault(); } \
	Type& operator=(const DefaultFlag &) { setDefault(); return *this; } \
public:

#define STRUCT(Type) \
STRUCT_NO_OSTREAM(Type) \
	friend auto operator<<(std::ostream &stream, const S& self) -> std::ostream & { \
		return stream << "<wgpu::" << #Type << ">"; \
	} \
public:

#define ENUM(Type) \
class Type { \
public: \
	typedef Type S; /* S == Self */ \
	typedef WGPU ## Type W; /* W == WGPU Type */ \
	constexpr Type() : m_raw(W{}) {} /* Using default value-initialization */ \
	constexpr Type(const W& w) : m_raw(w) {} \
	constexpr operator W() const { return m_raw; } \
	W m_raw; /* Ideally, this would be private, but then types generated with this macro would not be structural. */

#define ENUM_ENTRY(Name, Value) \
	static constexpr W Name = (W)(Value);

#define END };



// Other type aliases
using Flags = uint64_t;
using Bool = uint32_t;
using SubmissionIndex = uint64_t;

// Enumerations
ENUM(AdapterType)
	ENUM_ENTRY(DiscreteGPU, WGPUAdapterType_DiscreteGPU)
	ENUM_ENTRY(IntegratedGPU, WGPUAdapterType_IntegratedGPU)
	ENUM_ENTRY(CPU, WGPUAdapterType_CPU)
	ENUM_ENTRY(Unknown, WGPUAdapterType_Unknown)
	ENUM_ENTRY(Force32, WGPUAdapterType_Force32)
END
ENUM(AddressMode)
	ENUM_ENTRY(Undefined, WGPUAddressMode_Undefined)
	ENUM_ENTRY(ClampToEdge, WGPUAddressMode_ClampToEdge)
	ENUM_ENTRY(Repeat, WGPUAddressMode_Repeat)
	ENUM_ENTRY(MirrorRepeat, WGPUAddressMode_MirrorRepeat)
	ENUM_ENTRY(Force32, WGPUAddressMode_Force32)
END
ENUM(BackendType)
	ENUM_ENTRY(Undefined, WGPUBackendType_Undefined)
	ENUM_ENTRY(Null, WGPUBackendType_Null)
	ENUM_ENTRY(WebGPU, WGPUBackendType_WebGPU)
	ENUM_ENTRY(D3D11, WGPUBackendType_D3D11)
	ENUM_ENTRY(D3D12, WGPUBackendType_D3D12)
	ENUM_ENTRY(Metal, WGPUBackendType_Metal)
	ENUM_ENTRY(Vulkan, WGPUBackendType_Vulkan)
	ENUM_ENTRY(OpenGL, WGPUBackendType_OpenGL)
	ENUM_ENTRY(OpenGLES, WGPUBackendType_OpenGLES)
	ENUM_ENTRY(Force32, WGPUBackendType_Force32)
END
ENUM(BlendFactor)
	ENUM_ENTRY(Undefined, WGPUBlendFactor_Undefined)
	ENUM_ENTRY(Zero, WGPUBlendFactor_Zero)
	ENUM_ENTRY(One, WGPUBlendFactor_One)
	ENUM_ENTRY(Src, WGPUBlendFactor_Src)
	ENUM_ENTRY(OneMinusSrc, WGPUBlendFactor_OneMinusSrc)
	ENUM_ENTRY(SrcAlpha, WGPUBlendFactor_SrcAlpha)
	ENUM_ENTRY(OneMinusSrcAlpha, WGPUBlendFactor_OneMinusSrcAlpha)
	ENUM_ENTRY(Dst, WGPUBlendFactor_Dst)
	ENUM_ENTRY(OneMinusDst, WGPUBlendFactor_OneMinusDst)
	ENUM_ENTRY(DstAlpha, WGPUBlendFactor_DstAlpha)
	ENUM_ENTRY(OneMinusDstAlpha, WGPUBlendFactor_OneMinusDstAlpha)
	ENUM_ENTRY(SrcAlphaSaturated, WGPUBlendFactor_SrcAlphaSaturated)
	ENUM_ENTRY(Constant, WGPUBlendFactor_Constant)
	ENUM_ENTRY(OneMinusConstant, WGPUBlendFactor_OneMinusConstant)
	ENUM_ENTRY(Src1, WGPUBlendFactor_Src1)
	ENUM_ENTRY(OneMinusSrc1, WGPUBlendFactor_OneMinusSrc1)
	ENUM_ENTRY(Src1Alpha, WGPUBlendFactor_Src1Alpha)
	ENUM_ENTRY(OneMinusSrc1Alpha, WGPUBlendFactor_OneMinusSrc1Alpha)
	ENUM_ENTRY(Force32, WGPUBlendFactor_Force32)
END
ENUM(BlendOperation)
	ENUM_ENTRY(Undefined, WGPUBlendOperation_Undefined)
	ENUM_ENTRY(Add, WGPUBlendOperation_Add)
	ENUM_ENTRY(Subtract, WGPUBlendOperation_Subtract)
	ENUM_ENTRY(ReverseSubtract, WGPUBlendOperation_ReverseSubtract)
	ENUM_ENTRY(Min, WGPUBlendOperation_Min)
	ENUM_ENTRY(Max, WGPUBlendOperation_Max)
	ENUM_ENTRY(Force32, WGPUBlendOperation_Force32)
END
ENUM(BufferBindingType)
	ENUM_ENTRY(BindingNotUsed, WGPUBufferBindingType_BindingNotUsed)
	ENUM_ENTRY(Undefined, WGPUBufferBindingType_Undefined)
	ENUM_ENTRY(Uniform, WGPUBufferBindingType_Uniform)
	ENUM_ENTRY(Storage, WGPUBufferBindingType_Storage)
	ENUM_ENTRY(ReadOnlyStorage, WGPUBufferBindingType_ReadOnlyStorage)
	ENUM_ENTRY(Force32, WGPUBufferBindingType_Force32)
END
ENUM(BufferMapState)
	ENUM_ENTRY(Unmapped, WGPUBufferMapState_Unmapped)
	ENUM_ENTRY(Pending, WGPUBufferMapState_Pending)
	ENUM_ENTRY(Mapped, WGPUBufferMapState_Mapped)
	ENUM_ENTRY(Force32, WGPUBufferMapState_Force32)
END
ENUM(CallbackMode)
	ENUM_ENTRY(WaitAnyOnly, WGPUCallbackMode_WaitAnyOnly)
	ENUM_ENTRY(AllowProcessEvents, WGPUCallbackMode_AllowProcessEvents)
	ENUM_ENTRY(AllowSpontaneous, WGPUCallbackMode_AllowSpontaneous)
	ENUM_ENTRY(Force32, WGPUCallbackMode_Force32)
END
ENUM(CompareFunction)
	ENUM_ENTRY(Undefined, WGPUCompareFunction_Undefined)
	ENUM_ENTRY(Never, WGPUCompareFunction_Never)
	ENUM_ENTRY(Less, WGPUCompareFunction_Less)
	ENUM_ENTRY(Equal, WGPUCompareFunction_Equal)
	ENUM_ENTRY(LessEqual, WGPUCompareFunction_LessEqual)
	ENUM_ENTRY(Greater, WGPUCompareFunction_Greater)
	ENUM_ENTRY(NotEqual, WGPUCompareFunction_NotEqual)
	ENUM_ENTRY(GreaterEqual, WGPUCompareFunction_GreaterEqual)
	ENUM_ENTRY(Always, WGPUCompareFunction_Always)
	ENUM_ENTRY(Force32, WGPUCompareFunction_Force32)
END
ENUM(CompilationInfoRequestStatus)
	ENUM_ENTRY(Success, WGPUCompilationInfoRequestStatus_Success)
	ENUM_ENTRY(InstanceDropped, WGPUCompilationInfoRequestStatus_InstanceDropped)
	ENUM_ENTRY(Error, WGPUCompilationInfoRequestStatus_Error)
	ENUM_ENTRY(Unknown, WGPUCompilationInfoRequestStatus_Unknown)
	ENUM_ENTRY(Force32, WGPUCompilationInfoRequestStatus_Force32)
END
ENUM(CompilationMessageType)
	ENUM_ENTRY(Error, WGPUCompilationMessageType_Error)
	ENUM_ENTRY(Warning, WGPUCompilationMessageType_Warning)
	ENUM_ENTRY(Info, WGPUCompilationMessageType_Info)
	ENUM_ENTRY(Force32, WGPUCompilationMessageType_Force32)
END
ENUM(CompositeAlphaMode)
	ENUM_ENTRY(Auto, WGPUCompositeAlphaMode_Auto)
	ENUM_ENTRY(Opaque, WGPUCompositeAlphaMode_Opaque)
	ENUM_ENTRY(Premultiplied, WGPUCompositeAlphaMode_Premultiplied)
	ENUM_ENTRY(Unpremultiplied, WGPUCompositeAlphaMode_Unpremultiplied)
	ENUM_ENTRY(Inherit, WGPUCompositeAlphaMode_Inherit)
	ENUM_ENTRY(Force32, WGPUCompositeAlphaMode_Force32)
END
ENUM(CreatePipelineAsyncStatus)
	ENUM_ENTRY(Success, WGPUCreatePipelineAsyncStatus_Success)
	ENUM_ENTRY(InstanceDropped, WGPUCreatePipelineAsyncStatus_InstanceDropped)
	ENUM_ENTRY(ValidationError, WGPUCreatePipelineAsyncStatus_ValidationError)
	ENUM_ENTRY(InternalError, WGPUCreatePipelineAsyncStatus_InternalError)
	ENUM_ENTRY(Unknown, WGPUCreatePipelineAsyncStatus_Unknown)
	ENUM_ENTRY(Force32, WGPUCreatePipelineAsyncStatus_Force32)
END
ENUM(CullMode)
	ENUM_ENTRY(Undefined, WGPUCullMode_Undefined)
	ENUM_ENTRY(None, WGPUCullMode_None)
	ENUM_ENTRY(Front, WGPUCullMode_Front)
	ENUM_ENTRY(Back, WGPUCullMode_Back)
	ENUM_ENTRY(Force32, WGPUCullMode_Force32)
END
ENUM(DeviceLostReason)
	ENUM_ENTRY(Unknown, WGPUDeviceLostReason_Unknown)
	ENUM_ENTRY(Destroyed, WGPUDeviceLostReason_Destroyed)
	ENUM_ENTRY(InstanceDropped, WGPUDeviceLostReason_InstanceDropped)
	ENUM_ENTRY(FailedCreation, WGPUDeviceLostReason_FailedCreation)
	ENUM_ENTRY(Force32, WGPUDeviceLostReason_Force32)
END
ENUM(ErrorFilter)
	ENUM_ENTRY(Validation, WGPUErrorFilter_Validation)
	ENUM_ENTRY(OutOfMemory, WGPUErrorFilter_OutOfMemory)
	ENUM_ENTRY(Internal, WGPUErrorFilter_Internal)
	ENUM_ENTRY(Force32, WGPUErrorFilter_Force32)
END
ENUM(ErrorType)
	ENUM_ENTRY(NoError, WGPUErrorType_NoError)
	ENUM_ENTRY(Validation, WGPUErrorType_Validation)
	ENUM_ENTRY(OutOfMemory, WGPUErrorType_OutOfMemory)
	ENUM_ENTRY(Internal, WGPUErrorType_Internal)
	ENUM_ENTRY(Unknown, WGPUErrorType_Unknown)
	ENUM_ENTRY(Force32, WGPUErrorType_Force32)
END
ENUM(FeatureLevel)
	ENUM_ENTRY(Compatibility, WGPUFeatureLevel_Compatibility)
	ENUM_ENTRY(Core, WGPUFeatureLevel_Core)
	ENUM_ENTRY(Force32, WGPUFeatureLevel_Force32)
END
ENUM(FeatureName)
	ENUM_ENTRY(Undefined, WGPUFeatureName_Undefined)
	ENUM_ENTRY(DepthClipControl, WGPUFeatureName_DepthClipControl)
	ENUM_ENTRY(Depth32FloatStencil8, WGPUFeatureName_Depth32FloatStencil8)
	ENUM_ENTRY(TimestampQuery, WGPUFeatureName_TimestampQuery)
	ENUM_ENTRY(TextureCompressionBC, WGPUFeatureName_TextureCompressionBC)
	ENUM_ENTRY(TextureCompressionBCSliced3D, WGPUFeatureName_TextureCompressionBCSliced3D)
	ENUM_ENTRY(TextureCompressionETC2, WGPUFeatureName_TextureCompressionETC2)
	ENUM_ENTRY(TextureCompressionASTC, WGPUFeatureName_TextureCompressionASTC)
	ENUM_ENTRY(TextureCompressionASTCSliced3D, WGPUFeatureName_TextureCompressionASTCSliced3D)
	ENUM_ENTRY(IndirectFirstInstance, WGPUFeatureName_IndirectFirstInstance)
	ENUM_ENTRY(ShaderF16, WGPUFeatureName_ShaderF16)
	ENUM_ENTRY(RG11B10UfloatRenderable, WGPUFeatureName_RG11B10UfloatRenderable)
	ENUM_ENTRY(BGRA8UnormStorage, WGPUFeatureName_BGRA8UnormStorage)
	ENUM_ENTRY(Float32Filterable, WGPUFeatureName_Float32Filterable)
	ENUM_ENTRY(Float32Blendable, WGPUFeatureName_Float32Blendable)
	ENUM_ENTRY(ClipDistances, WGPUFeatureName_ClipDistances)
	ENUM_ENTRY(DualSourceBlending, WGPUFeatureName_DualSourceBlending)
	ENUM_ENTRY(Force32, WGPUFeatureName_Force32)
END
ENUM(FilterMode)
	ENUM_ENTRY(Undefined, WGPUFilterMode_Undefined)
	ENUM_ENTRY(Nearest, WGPUFilterMode_Nearest)
	ENUM_ENTRY(Linear, WGPUFilterMode_Linear)
	ENUM_ENTRY(Force32, WGPUFilterMode_Force32)
END
ENUM(FrontFace)
	ENUM_ENTRY(Undefined, WGPUFrontFace_Undefined)
	ENUM_ENTRY(CCW, WGPUFrontFace_CCW)
	ENUM_ENTRY(CW, WGPUFrontFace_CW)
	ENUM_ENTRY(Force32, WGPUFrontFace_Force32)
END
ENUM(IndexFormat)
	ENUM_ENTRY(Undefined, WGPUIndexFormat_Undefined)
	ENUM_ENTRY(Uint16, WGPUIndexFormat_Uint16)
	ENUM_ENTRY(Uint32, WGPUIndexFormat_Uint32)
	ENUM_ENTRY(Force32, WGPUIndexFormat_Force32)
END
ENUM(LoadOp)
	ENUM_ENTRY(Undefined, WGPULoadOp_Undefined)
	ENUM_ENTRY(Load, WGPULoadOp_Load)
	ENUM_ENTRY(Clear, WGPULoadOp_Clear)
	ENUM_ENTRY(Force32, WGPULoadOp_Force32)
END
ENUM(MapAsyncStatus)
	ENUM_ENTRY(Success, WGPUMapAsyncStatus_Success)
	ENUM_ENTRY(InstanceDropped, WGPUMapAsyncStatus_InstanceDropped)
	ENUM_ENTRY(Error, WGPUMapAsyncStatus_Error)
	ENUM_ENTRY(Aborted, WGPUMapAsyncStatus_Aborted)
	ENUM_ENTRY(Unknown, WGPUMapAsyncStatus_Unknown)
	ENUM_ENTRY(Force32, WGPUMapAsyncStatus_Force32)
END
ENUM(MipmapFilterMode)
	ENUM_ENTRY(Undefined, WGPUMipmapFilterMode_Undefined)
	ENUM_ENTRY(Nearest, WGPUMipmapFilterMode_Nearest)
	ENUM_ENTRY(Linear, WGPUMipmapFilterMode_Linear)
	ENUM_ENTRY(Force32, WGPUMipmapFilterMode_Force32)
END
ENUM(OptionalBool)
	ENUM_ENTRY(False, WGPUOptionalBool_False)
	ENUM_ENTRY(True, WGPUOptionalBool_True)
	ENUM_ENTRY(Undefined, WGPUOptionalBool_Undefined)
	ENUM_ENTRY(Force32, WGPUOptionalBool_Force32)
END
ENUM(PopErrorScopeStatus)
	ENUM_ENTRY(Success, WGPUPopErrorScopeStatus_Success)
	ENUM_ENTRY(InstanceDropped, WGPUPopErrorScopeStatus_InstanceDropped)
	ENUM_ENTRY(EmptyStack, WGPUPopErrorScopeStatus_EmptyStack)
	ENUM_ENTRY(Force32, WGPUPopErrorScopeStatus_Force32)
END
ENUM(PowerPreference)
	ENUM_ENTRY(Undefined, WGPUPowerPreference_Undefined)
	ENUM_ENTRY(LowPower, WGPUPowerPreference_LowPower)
	ENUM_ENTRY(HighPerformance, WGPUPowerPreference_HighPerformance)
	ENUM_ENTRY(Force32, WGPUPowerPreference_Force32)
END
ENUM(PresentMode)
	ENUM_ENTRY(Undefined, WGPUPresentMode_Undefined)
	ENUM_ENTRY(Fifo, WGPUPresentMode_Fifo)
	ENUM_ENTRY(FifoRelaxed, WGPUPresentMode_FifoRelaxed)
	ENUM_ENTRY(Immediate, WGPUPresentMode_Immediate)
	ENUM_ENTRY(Mailbox, WGPUPresentMode_Mailbox)
	ENUM_ENTRY(Force32, WGPUPresentMode_Force32)
END
ENUM(PrimitiveTopology)
	ENUM_ENTRY(Undefined, WGPUPrimitiveTopology_Undefined)
	ENUM_ENTRY(PointList, WGPUPrimitiveTopology_PointList)
	ENUM_ENTRY(LineList, WGPUPrimitiveTopology_LineList)
	ENUM_ENTRY(LineStrip, WGPUPrimitiveTopology_LineStrip)
	ENUM_ENTRY(TriangleList, WGPUPrimitiveTopology_TriangleList)
	ENUM_ENTRY(TriangleStrip, WGPUPrimitiveTopology_TriangleStrip)
	ENUM_ENTRY(Force32, WGPUPrimitiveTopology_Force32)
END
ENUM(QueryType)
	ENUM_ENTRY(Occlusion, WGPUQueryType_Occlusion)
	ENUM_ENTRY(Timestamp, WGPUQueryType_Timestamp)
	ENUM_ENTRY(Force32, WGPUQueryType_Force32)
END
ENUM(QueueWorkDoneStatus)
	ENUM_ENTRY(Success, WGPUQueueWorkDoneStatus_Success)
	ENUM_ENTRY(InstanceDropped, WGPUQueueWorkDoneStatus_InstanceDropped)
	ENUM_ENTRY(Error, WGPUQueueWorkDoneStatus_Error)
	ENUM_ENTRY(Unknown, WGPUQueueWorkDoneStatus_Unknown)
	ENUM_ENTRY(Force32, WGPUQueueWorkDoneStatus_Force32)
END
ENUM(RequestAdapterStatus)
	ENUM_ENTRY(Success, WGPURequestAdapterStatus_Success)
	ENUM_ENTRY(InstanceDropped, WGPURequestAdapterStatus_InstanceDropped)
	ENUM_ENTRY(Unavailable, WGPURequestAdapterStatus_Unavailable)
	ENUM_ENTRY(Error, WGPURequestAdapterStatus_Error)
	ENUM_ENTRY(Unknown, WGPURequestAdapterStatus_Unknown)
	ENUM_ENTRY(Force32, WGPURequestAdapterStatus_Force32)
END
ENUM(RequestDeviceStatus)
	ENUM_ENTRY(Success, WGPURequestDeviceStatus_Success)
	ENUM_ENTRY(InstanceDropped, WGPURequestDeviceStatus_InstanceDropped)
	ENUM_ENTRY(Error, WGPURequestDeviceStatus_Error)
	ENUM_ENTRY(Unknown, WGPURequestDeviceStatus_Unknown)
	ENUM_ENTRY(Force32, WGPURequestDeviceStatus_Force32)
END
ENUM(SType)
	ENUM_ENTRY(ShaderSourceSPIRV, WGPUSType_ShaderSourceSPIRV)
	ENUM_ENTRY(ShaderSourceWGSL, WGPUSType_ShaderSourceWGSL)
	ENUM_ENTRY(RenderPassMaxDrawCount, WGPUSType_RenderPassMaxDrawCount)
	ENUM_ENTRY(SurfaceSourceMetalLayer, WGPUSType_SurfaceSourceMetalLayer)
	ENUM_ENTRY(SurfaceSourceWindowsHWND, WGPUSType_SurfaceSourceWindowsHWND)
	ENUM_ENTRY(SurfaceSourceXlibWindow, WGPUSType_SurfaceSourceXlibWindow)
	ENUM_ENTRY(SurfaceSourceWaylandSurface, WGPUSType_SurfaceSourceWaylandSurface)
	ENUM_ENTRY(SurfaceSourceAndroidNativeWindow, WGPUSType_SurfaceSourceAndroidNativeWindow)
	ENUM_ENTRY(SurfaceSourceXCBWindow, WGPUSType_SurfaceSourceXCBWindow)
	ENUM_ENTRY(Force32, WGPUSType_Force32)
END
ENUM(SamplerBindingType)
	ENUM_ENTRY(BindingNotUsed, WGPUSamplerBindingType_BindingNotUsed)
	ENUM_ENTRY(Undefined, WGPUSamplerBindingType_Undefined)
	ENUM_ENTRY(Filtering, WGPUSamplerBindingType_Filtering)
	ENUM_ENTRY(NonFiltering, WGPUSamplerBindingType_NonFiltering)
	ENUM_ENTRY(Comparison, WGPUSamplerBindingType_Comparison)
	ENUM_ENTRY(Force32, WGPUSamplerBindingType_Force32)
END
ENUM(Status)
	ENUM_ENTRY(Success, WGPUStatus_Success)
	ENUM_ENTRY(Error, WGPUStatus_Error)
	ENUM_ENTRY(Force32, WGPUStatus_Force32)
END
ENUM(StencilOperation)
	ENUM_ENTRY(Undefined, WGPUStencilOperation_Undefined)
	ENUM_ENTRY(Keep, WGPUStencilOperation_Keep)
	ENUM_ENTRY(Zero, WGPUStencilOperation_Zero)
	ENUM_ENTRY(Replace, WGPUStencilOperation_Replace)
	ENUM_ENTRY(Invert, WGPUStencilOperation_Invert)
	ENUM_ENTRY(IncrementClamp, WGPUStencilOperation_IncrementClamp)
	ENUM_ENTRY(DecrementClamp, WGPUStencilOperation_DecrementClamp)
	ENUM_ENTRY(IncrementWrap, WGPUStencilOperation_IncrementWrap)
	ENUM_ENTRY(DecrementWrap, WGPUStencilOperation_DecrementWrap)
	ENUM_ENTRY(Force32, WGPUStencilOperation_Force32)
END
ENUM(StorageTextureAccess)
	ENUM_ENTRY(BindingNotUsed, WGPUStorageTextureAccess_BindingNotUsed)
	ENUM_ENTRY(Undefined, WGPUStorageTextureAccess_Undefined)
	ENUM_ENTRY(WriteOnly, WGPUStorageTextureAccess_WriteOnly)
	ENUM_ENTRY(ReadOnly, WGPUStorageTextureAccess_ReadOnly)
	ENUM_ENTRY(ReadWrite, WGPUStorageTextureAccess_ReadWrite)
	ENUM_ENTRY(Force32, WGPUStorageTextureAccess_Force32)
END
ENUM(StoreOp)
	ENUM_ENTRY(Undefined, WGPUStoreOp_Undefined)
	ENUM_ENTRY(Store, WGPUStoreOp_Store)
	ENUM_ENTRY(Discard, WGPUStoreOp_Discard)
	ENUM_ENTRY(Force32, WGPUStoreOp_Force32)
END
ENUM(SurfaceGetCurrentTextureStatus)
	ENUM_ENTRY(SuccessOptimal, WGPUSurfaceGetCurrentTextureStatus_SuccessOptimal)
	ENUM_ENTRY(SuccessSuboptimal, WGPUSurfaceGetCurrentTextureStatus_SuccessSuboptimal)
	ENUM_ENTRY(Timeout, WGPUSurfaceGetCurrentTextureStatus_Timeout)
	ENUM_ENTRY(Outdated, WGPUSurfaceGetCurrentTextureStatus_Outdated)
	ENUM_ENTRY(Lost, WGPUSurfaceGetCurrentTextureStatus_Lost)
	ENUM_ENTRY(OutOfMemory, WGPUSurfaceGetCurrentTextureStatus_OutOfMemory)
	ENUM_ENTRY(DeviceLost, WGPUSurfaceGetCurrentTextureStatus_DeviceLost)
	ENUM_ENTRY(Error, WGPUSurfaceGetCurrentTextureStatus_Error)
	ENUM_ENTRY(Force32, WGPUSurfaceGetCurrentTextureStatus_Force32)
END
ENUM(TextureAspect)
	ENUM_ENTRY(Undefined, WGPUTextureAspect_Undefined)
	ENUM_ENTRY(All, WGPUTextureAspect_All)
	ENUM_ENTRY(StencilOnly, WGPUTextureAspect_StencilOnly)
	ENUM_ENTRY(DepthOnly, WGPUTextureAspect_DepthOnly)
	ENUM_ENTRY(Force32, WGPUTextureAspect_Force32)
END
ENUM(TextureDimension)
	ENUM_ENTRY(Undefined, WGPUTextureDimension_Undefined)
	ENUM_ENTRY(_1D, WGPUTextureDimension_1D)
	ENUM_ENTRY(_2D, WGPUTextureDimension_2D)
	ENUM_ENTRY(_3D, WGPUTextureDimension_3D)
	ENUM_ENTRY(Force32, WGPUTextureDimension_Force32)
END
ENUM(TextureFormat)
	ENUM_ENTRY(Undefined, WGPUTextureFormat_Undefined)
	ENUM_ENTRY(R8Unorm, WGPUTextureFormat_R8Unorm)
	ENUM_ENTRY(R8Snorm, WGPUTextureFormat_R8Snorm)
	ENUM_ENTRY(R8Uint, WGPUTextureFormat_R8Uint)
	ENUM_ENTRY(R8Sint, WGPUTextureFormat_R8Sint)
	ENUM_ENTRY(R16Uint, WGPUTextureFormat_R16Uint)
	ENUM_ENTRY(R16Sint, WGPUTextureFormat_R16Sint)
	ENUM_ENTRY(R16Float, WGPUTextureFormat_R16Float)
	ENUM_ENTRY(RG8Unorm, WGPUTextureFormat_RG8Unorm)
	ENUM_ENTRY(RG8Snorm, WGPUTextureFormat_RG8Snorm)
	ENUM_ENTRY(RG8Uint, WGPUTextureFormat_RG8Uint)
	ENUM_ENTRY(RG8Sint, WGPUTextureFormat_RG8Sint)
	ENUM_ENTRY(R32Float, WGPUTextureFormat_R32Float)
	ENUM_ENTRY(R32Uint, WGPUTextureFormat_R32Uint)
	ENUM_ENTRY(R32Sint, WGPUTextureFormat_R32Sint)
	ENUM_ENTRY(RG16Uint, WGPUTextureFormat_RG16Uint)
	ENUM_ENTRY(RG16Sint, WGPUTextureFormat_RG16Sint)
	ENUM_ENTRY(RG16Float, WGPUTextureFormat_RG16Float)
	ENUM_ENTRY(RGBA8Unorm, WGPUTextureFormat_RGBA8Unorm)
	ENUM_ENTRY(RGBA8UnormSrgb, WGPUTextureFormat_RGBA8UnormSrgb)
	ENUM_ENTRY(RGBA8Snorm, WGPUTextureFormat_RGBA8Snorm)
	ENUM_ENTRY(RGBA8Uint, WGPUTextureFormat_RGBA8Uint)
	ENUM_ENTRY(RGBA8Sint, WGPUTextureFormat_RGBA8Sint)
	ENUM_ENTRY(BGRA8Unorm, WGPUTextureFormat_BGRA8Unorm)
	ENUM_ENTRY(BGRA8UnormSrgb, WGPUTextureFormat_BGRA8UnormSrgb)
	ENUM_ENTRY(RGB10A2Uint, WGPUTextureFormat_RGB10A2Uint)
	ENUM_ENTRY(RGB10A2Unorm, WGPUTextureFormat_RGB10A2Unorm)
	ENUM_ENTRY(RG11B10Ufloat, WGPUTextureFormat_RG11B10Ufloat)
	ENUM_ENTRY(RGB9E5Ufloat, WGPUTextureFormat_RGB9E5Ufloat)
	ENUM_ENTRY(RG32Float, WGPUTextureFormat_RG32Float)
	ENUM_ENTRY(RG32Uint, WGPUTextureFormat_RG32Uint)
	ENUM_ENTRY(RG32Sint, WGPUTextureFormat_RG32Sint)
	ENUM_ENTRY(RGBA16Uint, WGPUTextureFormat_RGBA16Uint)
	ENUM_ENTRY(RGBA16Sint, WGPUTextureFormat_RGBA16Sint)
	ENUM_ENTRY(RGBA16Float, WGPUTextureFormat_RGBA16Float)
	ENUM_ENTRY(RGBA32Float, WGPUTextureFormat_RGBA32Float)
	ENUM_ENTRY(RGBA32Uint, WGPUTextureFormat_RGBA32Uint)
	ENUM_ENTRY(RGBA32Sint, WGPUTextureFormat_RGBA32Sint)
	ENUM_ENTRY(Stencil8, WGPUTextureFormat_Stencil8)
	ENUM_ENTRY(Depth16Unorm, WGPUTextureFormat_Depth16Unorm)
	ENUM_ENTRY(Depth24Plus, WGPUTextureFormat_Depth24Plus)
	ENUM_ENTRY(Depth24PlusStencil8, WGPUTextureFormat_Depth24PlusStencil8)
	ENUM_ENTRY(Depth32Float, WGPUTextureFormat_Depth32Float)
	ENUM_ENTRY(Depth32FloatStencil8, WGPUTextureFormat_Depth32FloatStencil8)
	ENUM_ENTRY(BC1RGBAUnorm, WGPUTextureFormat_BC1RGBAUnorm)
	ENUM_ENTRY(BC1RGBAUnormSrgb, WGPUTextureFormat_BC1RGBAUnormSrgb)
	ENUM_ENTRY(BC2RGBAUnorm, WGPUTextureFormat_BC2RGBAUnorm)
	ENUM_ENTRY(BC2RGBAUnormSrgb, WGPUTextureFormat_BC2RGBAUnormSrgb)
	ENUM_ENTRY(BC3RGBAUnorm, WGPUTextureFormat_BC3RGBAUnorm)
	ENUM_ENTRY(BC3RGBAUnormSrgb, WGPUTextureFormat_BC3RGBAUnormSrgb)
	ENUM_ENTRY(BC4RUnorm, WGPUTextureFormat_BC4RUnorm)
	ENUM_ENTRY(BC4RSnorm, WGPUTextureFormat_BC4RSnorm)
	ENUM_ENTRY(BC5RGUnorm, WGPUTextureFormat_BC5RGUnorm)
	ENUM_ENTRY(BC5RGSnorm, WGPUTextureFormat_BC5RGSnorm)
	ENUM_ENTRY(BC6HRGBUfloat, WGPUTextureFormat_BC6HRGBUfloat)
	ENUM_ENTRY(BC6HRGBFloat, WGPUTextureFormat_BC6HRGBFloat)
	ENUM_ENTRY(BC7RGBAUnorm, WGPUTextureFormat_BC7RGBAUnorm)
	ENUM_ENTRY(BC7RGBAUnormSrgb, WGPUTextureFormat_BC7RGBAUnormSrgb)
	ENUM_ENTRY(ETC2RGB8Unorm, WGPUTextureFormat_ETC2RGB8Unorm)
	ENUM_ENTRY(ETC2RGB8UnormSrgb, WGPUTextureFormat_ETC2RGB8UnormSrgb)
	ENUM_ENTRY(ETC2RGB8A1Unorm, WGPUTextureFormat_ETC2RGB8A1Unorm)
	ENUM_ENTRY(ETC2RGB8A1UnormSrgb, WGPUTextureFormat_ETC2RGB8A1UnormSrgb)
	ENUM_ENTRY(ETC2RGBA8Unorm, WGPUTextureFormat_ETC2RGBA8Unorm)
	ENUM_ENTRY(ETC2RGBA8UnormSrgb, WGPUTextureFormat_ETC2RGBA8UnormSrgb)
	ENUM_ENTRY(EACR11Unorm, WGPUTextureFormat_EACR11Unorm)
	ENUM_ENTRY(EACR11Snorm, WGPUTextureFormat_EACR11Snorm)
	ENUM_ENTRY(EACRG11Unorm, WGPUTextureFormat_EACRG11Unorm)
	ENUM_ENTRY(EACRG11Snorm, WGPUTextureFormat_EACRG11Snorm)
	ENUM_ENTRY(ASTC4x4Unorm, WGPUTextureFormat_ASTC4x4Unorm)
	ENUM_ENTRY(ASTC4x4UnormSrgb, WGPUTextureFormat_ASTC4x4UnormSrgb)
	ENUM_ENTRY(ASTC5x4Unorm, WGPUTextureFormat_ASTC5x4Unorm)
	ENUM_ENTRY(ASTC5x4UnormSrgb, WGPUTextureFormat_ASTC5x4UnormSrgb)
	ENUM_ENTRY(ASTC5x5Unorm, WGPUTextureFormat_ASTC5x5Unorm)
	ENUM_ENTRY(ASTC5x5UnormSrgb, WGPUTextureFormat_ASTC5x5UnormSrgb)
	ENUM_ENTRY(ASTC6x5Unorm, WGPUTextureFormat_ASTC6x5Unorm)
	ENUM_ENTRY(ASTC6x5UnormSrgb, WGPUTextureFormat_ASTC6x5UnormSrgb)
	ENUM_ENTRY(ASTC6x6Unorm, WGPUTextureFormat_ASTC6x6Unorm)
	ENUM_ENTRY(ASTC6x6UnormSrgb, WGPUTextureFormat_ASTC6x6UnormSrgb)
	ENUM_ENTRY(ASTC8x5Unorm, WGPUTextureFormat_ASTC8x5Unorm)
	ENUM_ENTRY(ASTC8x5UnormSrgb, WGPUTextureFormat_ASTC8x5UnormSrgb)
	ENUM_ENTRY(ASTC8x6Unorm, WGPUTextureFormat_ASTC8x6Unorm)
	ENUM_ENTRY(ASTC8x6UnormSrgb, WGPUTextureFormat_ASTC8x6UnormSrgb)
	ENUM_ENTRY(ASTC8x8Unorm, WGPUTextureFormat_ASTC8x8Unorm)
	ENUM_ENTRY(ASTC8x8UnormSrgb, WGPUTextureFormat_ASTC8x8UnormSrgb)
	ENUM_ENTRY(ASTC10x5Unorm, WGPUTextureFormat_ASTC10x5Unorm)
	ENUM_ENTRY(ASTC10x5UnormSrgb, WGPUTextureFormat_ASTC10x5UnormSrgb)
	ENUM_ENTRY(ASTC10x6Unorm, WGPUTextureFormat_ASTC10x6Unorm)
	ENUM_ENTRY(ASTC10x6UnormSrgb, WGPUTextureFormat_ASTC10x6UnormSrgb)
	ENUM_ENTRY(ASTC10x8Unorm, WGPUTextureFormat_ASTC10x8Unorm)
	ENUM_ENTRY(ASTC10x8UnormSrgb, WGPUTextureFormat_ASTC10x8UnormSrgb)
	ENUM_ENTRY(ASTC10x10Unorm, WGPUTextureFormat_ASTC10x10Unorm)
	ENUM_ENTRY(ASTC10x10UnormSrgb, WGPUTextureFormat_ASTC10x10UnormSrgb)
	ENUM_ENTRY(ASTC12x10Unorm, WGPUTextureFormat_ASTC12x10Unorm)
	ENUM_ENTRY(ASTC12x10UnormSrgb, WGPUTextureFormat_ASTC12x10UnormSrgb)
	ENUM_ENTRY(ASTC12x12Unorm, WGPUTextureFormat_ASTC12x12Unorm)
	ENUM_ENTRY(ASTC12x12UnormSrgb, WGPUTextureFormat_ASTC12x12UnormSrgb)
	ENUM_ENTRY(Force32, WGPUTextureFormat_Force32)
END
ENUM(TextureSampleType)
	ENUM_ENTRY(BindingNotUsed, WGPUTextureSampleType_BindingNotUsed)
	ENUM_ENTRY(Undefined, WGPUTextureSampleType_Undefined)
	ENUM_ENTRY(Float, WGPUTextureSampleType_Float)
	ENUM_ENTRY(UnfilterableFloat, WGPUTextureSampleType_UnfilterableFloat)
	ENUM_ENTRY(Depth, WGPUTextureSampleType_Depth)
	ENUM_ENTRY(Sint, WGPUTextureSampleType_Sint)
	ENUM_ENTRY(Uint, WGPUTextureSampleType_Uint)
	ENUM_ENTRY(Force32, WGPUTextureSampleType_Force32)
END
ENUM(TextureViewDimension)
	ENUM_ENTRY(Undefined, WGPUTextureViewDimension_Undefined)
	ENUM_ENTRY(_1D, WGPUTextureViewDimension_1D)
	ENUM_ENTRY(_2D, WGPUTextureViewDimension_2D)
	ENUM_ENTRY(_2DArray, WGPUTextureViewDimension_2DArray)
	ENUM_ENTRY(Cube, WGPUTextureViewDimension_Cube)
	ENUM_ENTRY(CubeArray, WGPUTextureViewDimension_CubeArray)
	ENUM_ENTRY(_3D, WGPUTextureViewDimension_3D)
	ENUM_ENTRY(Force32, WGPUTextureViewDimension_Force32)
END
ENUM(VertexFormat)
	ENUM_ENTRY(Uint8, WGPUVertexFormat_Uint8)
	ENUM_ENTRY(Uint8x2, WGPUVertexFormat_Uint8x2)
	ENUM_ENTRY(Uint8x4, WGPUVertexFormat_Uint8x4)
	ENUM_ENTRY(Sint8, WGPUVertexFormat_Sint8)
	ENUM_ENTRY(Sint8x2, WGPUVertexFormat_Sint8x2)
	ENUM_ENTRY(Sint8x4, WGPUVertexFormat_Sint8x4)
	ENUM_ENTRY(Unorm8, WGPUVertexFormat_Unorm8)
	ENUM_ENTRY(Unorm8x2, WGPUVertexFormat_Unorm8x2)
	ENUM_ENTRY(Unorm8x4, WGPUVertexFormat_Unorm8x4)
	ENUM_ENTRY(Snorm8, WGPUVertexFormat_Snorm8)
	ENUM_ENTRY(Snorm8x2, WGPUVertexFormat_Snorm8x2)
	ENUM_ENTRY(Snorm8x4, WGPUVertexFormat_Snorm8x4)
	ENUM_ENTRY(Uint16, WGPUVertexFormat_Uint16)
	ENUM_ENTRY(Uint16x2, WGPUVertexFormat_Uint16x2)
	ENUM_ENTRY(Uint16x4, WGPUVertexFormat_Uint16x4)
	ENUM_ENTRY(Sint16, WGPUVertexFormat_Sint16)
	ENUM_ENTRY(Sint16x2, WGPUVertexFormat_Sint16x2)
	ENUM_ENTRY(Sint16x4, WGPUVertexFormat_Sint16x4)
	ENUM_ENTRY(Unorm16, WGPUVertexFormat_Unorm16)
	ENUM_ENTRY(Unorm16x2, WGPUVertexFormat_Unorm16x2)
	ENUM_ENTRY(Unorm16x4, WGPUVertexFormat_Unorm16x4)
	ENUM_ENTRY(Snorm16, WGPUVertexFormat_Snorm16)
	ENUM_ENTRY(Snorm16x2, WGPUVertexFormat_Snorm16x2)
	ENUM_ENTRY(Snorm16x4, WGPUVertexFormat_Snorm16x4)
	ENUM_ENTRY(Float16, WGPUVertexFormat_Float16)
	ENUM_ENTRY(Float16x2, WGPUVertexFormat_Float16x2)
	ENUM_ENTRY(Float16x4, WGPUVertexFormat_Float16x4)
	ENUM_ENTRY(Float32, WGPUVertexFormat_Float32)
	ENUM_ENTRY(Float32x2, WGPUVertexFormat_Float32x2)
	ENUM_ENTRY(Float32x3, WGPUVertexFormat_Float32x3)
	ENUM_ENTRY(Float32x4, WGPUVertexFormat_Float32x4)
	ENUM_ENTRY(Uint32, WGPUVertexFormat_Uint32)
	ENUM_ENTRY(Uint32x2, WGPUVertexFormat_Uint32x2)
	ENUM_ENTRY(Uint32x3, WGPUVertexFormat_Uint32x3)
	ENUM_ENTRY(Uint32x4, WGPUVertexFormat_Uint32x4)
	ENUM_ENTRY(Sint32, WGPUVertexFormat_Sint32)
	ENUM_ENTRY(Sint32x2, WGPUVertexFormat_Sint32x2)
	ENUM_ENTRY(Sint32x3, WGPUVertexFormat_Sint32x3)
	ENUM_ENTRY(Sint32x4, WGPUVertexFormat_Sint32x4)
	ENUM_ENTRY(_2, WGPUVertexFormat_Unorm10_10_10_2)
	ENUM_ENTRY(Unorm8x4BGRA, WGPUVertexFormat_Unorm8x4BGRA)
	ENUM_ENTRY(Force32, WGPUVertexFormat_Force32)
END
ENUM(VertexStepMode)
	ENUM_ENTRY(VertexBufferNotUsed, WGPUVertexStepMode_VertexBufferNotUsed)
	ENUM_ENTRY(Undefined, WGPUVertexStepMode_Undefined)
	ENUM_ENTRY(Vertex, WGPUVertexStepMode_Vertex)
	ENUM_ENTRY(Instance, WGPUVertexStepMode_Instance)
	ENUM_ENTRY(Force32, WGPUVertexStepMode_Force32)
END
ENUM(WGSLLanguageFeatureName)
	ENUM_ENTRY(ReadonlyAndReadwriteStorageTextures, WGPUWGSLLanguageFeatureName_ReadonlyAndReadwriteStorageTextures)
	ENUM_ENTRY(Packed4x8IntegerDotProduct, WGPUWGSLLanguageFeatureName_Packed4x8IntegerDotProduct)
	ENUM_ENTRY(UnrestrictedPointerParameters, WGPUWGSLLanguageFeatureName_UnrestrictedPointerParameters)
	ENUM_ENTRY(PointerCompositeAccess, WGPUWGSLLanguageFeatureName_PointerCompositeAccess)
	ENUM_ENTRY(Force32, WGPUWGSLLanguageFeatureName_Force32)
END
ENUM(WaitStatus)
	ENUM_ENTRY(Success, WGPUWaitStatus_Success)
	ENUM_ENTRY(TimedOut, WGPUWaitStatus_TimedOut)
	ENUM_ENTRY(UnsupportedTimeout, WGPUWaitStatus_UnsupportedTimeout)
	ENUM_ENTRY(UnsupportedCount, WGPUWaitStatus_UnsupportedCount)
	ENUM_ENTRY(UnsupportedMixedSources, WGPUWaitStatus_UnsupportedMixedSources)
	ENUM_ENTRY(Force32, WGPUWaitStatus_Force32)
END
ENUM(BufferUsage)
	ENUM_ENTRY(None, 0x0000000000000000)
	ENUM_ENTRY(MapRead, 0x0000000000000001)
	ENUM_ENTRY(MapWrite, 0x0000000000000002)
	ENUM_ENTRY(CopySrc, 0x0000000000000004)
	ENUM_ENTRY(CopyDst, 0x0000000000000008)
	ENUM_ENTRY(Index, 0x0000000000000010)
	ENUM_ENTRY(Vertex, 0x0000000000000020)
	ENUM_ENTRY(Uniform, 0x0000000000000040)
	ENUM_ENTRY(Storage, 0x0000000000000080)
	ENUM_ENTRY(Indirect, 0x0000000000000100)
	ENUM_ENTRY(QueryResolve, 0x0000000000000200)
END
ENUM(ColorWriteMask)
	ENUM_ENTRY(None, 0x0000000000000000)
	ENUM_ENTRY(Red, 0x0000000000000001)
	ENUM_ENTRY(Green, 0x0000000000000002)
	ENUM_ENTRY(Blue, 0x0000000000000004)
	ENUM_ENTRY(Alpha, 0x0000000000000008)
	ENUM_ENTRY(All, 0x000000000000000F)
END
ENUM(MapMode)
	ENUM_ENTRY(None, 0x0000000000000000)
	ENUM_ENTRY(Read, 0x0000000000000001)
	ENUM_ENTRY(Write, 0x0000000000000002)
END
ENUM(ShaderStage)
	ENUM_ENTRY(None, 0x0000000000000000)
	ENUM_ENTRY(Vertex, 0x0000000000000001)
	ENUM_ENTRY(Fragment, 0x0000000000000002)
	ENUM_ENTRY(Compute, 0x0000000000000004)
END
ENUM(TextureUsage)
	ENUM_ENTRY(None, 0x0000000000000000)
	ENUM_ENTRY(CopySrc, 0x0000000000000001)
	ENUM_ENTRY(CopyDst, 0x0000000000000002)
	ENUM_ENTRY(TextureBinding, 0x0000000000000004)
	ENUM_ENTRY(StorageBinding, 0x0000000000000008)
	ENUM_ENTRY(RenderAttachment, 0x0000000000000010)
END
ENUM(NativeSType)
	ENUM_ENTRY(DeviceExtras, WGPUSType_DeviceExtras)
	ENUM_ENTRY(NativeLimits, WGPUSType_NativeLimits)
	ENUM_ENTRY(PipelineLayoutExtras, WGPUSType_PipelineLayoutExtras)
	ENUM_ENTRY(ShaderModuleGLSLDescriptor, WGPUSType_ShaderModuleGLSLDescriptor)
	ENUM_ENTRY(InstanceExtras, WGPUSType_InstanceExtras)
	ENUM_ENTRY(BindGroupEntryExtras, WGPUSType_BindGroupEntryExtras)
	ENUM_ENTRY(BindGroupLayoutEntryExtras, WGPUSType_BindGroupLayoutEntryExtras)
	ENUM_ENTRY(QuerySetDescriptorExtras, WGPUSType_QuerySetDescriptorExtras)
	ENUM_ENTRY(SurfaceConfigurationExtras, WGPUSType_SurfaceConfigurationExtras)
	ENUM_ENTRY(Force32, WGPUNativeSType_Force32)
END
ENUM(NativeFeature)
	ENUM_ENTRY(PushConstants, WGPUNativeFeature_PushConstants)
	ENUM_ENTRY(TextureAdapterSpecificFormatFeatures, WGPUNativeFeature_TextureAdapterSpecificFormatFeatures)
	ENUM_ENTRY(MultiDrawIndirect, WGPUNativeFeature_MultiDrawIndirect)
	ENUM_ENTRY(MultiDrawIndirectCount, WGPUNativeFeature_MultiDrawIndirectCount)
	ENUM_ENTRY(VertexWritableStorage, WGPUNativeFeature_VertexWritableStorage)
	ENUM_ENTRY(TextureBindingArray, WGPUNativeFeature_TextureBindingArray)
	ENUM_ENTRY(SampledTextureAndStorageBufferArrayNonUniformIndexing, WGPUNativeFeature_SampledTextureAndStorageBufferArrayNonUniformIndexing)
	ENUM_ENTRY(PipelineStatisticsQuery, WGPUNativeFeature_PipelineStatisticsQuery)
	ENUM_ENTRY(StorageResourceBindingArray, WGPUNativeFeature_StorageResourceBindingArray)
	ENUM_ENTRY(PartiallyBoundBindingArray, WGPUNativeFeature_PartiallyBoundBindingArray)
	ENUM_ENTRY(TextureFormat16bitNorm, WGPUNativeFeature_TextureFormat16bitNorm)
	ENUM_ENTRY(TextureCompressionAstcHdr, WGPUNativeFeature_TextureCompressionAstcHdr)
	ENUM_ENTRY(MappablePrimaryBuffers, WGPUNativeFeature_MappablePrimaryBuffers)
	ENUM_ENTRY(BufferBindingArray, WGPUNativeFeature_BufferBindingArray)
	ENUM_ENTRY(UniformBufferAndStorageTextureArrayNonUniformIndexing, WGPUNativeFeature_UniformBufferAndStorageTextureArrayNonUniformIndexing)
	ENUM_ENTRY(SpirvShaderPassthrough, WGPUNativeFeature_SpirvShaderPassthrough)
	ENUM_ENTRY(VertexAttribute64bit, WGPUNativeFeature_VertexAttribute64bit)
	ENUM_ENTRY(TextureFormatNv12, WGPUNativeFeature_TextureFormatNv12)
	ENUM_ENTRY(RayTracingAccelerationStructure, WGPUNativeFeature_RayTracingAccelerationStructure)
	ENUM_ENTRY(RayQuery, WGPUNativeFeature_RayQuery)
	ENUM_ENTRY(ShaderF64, WGPUNativeFeature_ShaderF64)
	ENUM_ENTRY(ShaderI16, WGPUNativeFeature_ShaderI16)
	ENUM_ENTRY(ShaderPrimitiveIndex, WGPUNativeFeature_ShaderPrimitiveIndex)
	ENUM_ENTRY(ShaderEarlyDepthTest, WGPUNativeFeature_ShaderEarlyDepthTest)
	ENUM_ENTRY(Subgroup, WGPUNativeFeature_Subgroup)
	ENUM_ENTRY(SubgroupVertex, WGPUNativeFeature_SubgroupVertex)
	ENUM_ENTRY(SubgroupBarrier, WGPUNativeFeature_SubgroupBarrier)
	ENUM_ENTRY(TimestampQueryInsideEncoders, WGPUNativeFeature_TimestampQueryInsideEncoders)
	ENUM_ENTRY(TimestampQueryInsidePasses, WGPUNativeFeature_TimestampQueryInsidePasses)
	ENUM_ENTRY(Force32, WGPUNativeFeature_Force32)
END
ENUM(LogLevel)
	ENUM_ENTRY(Off, WGPULogLevel_Off)
	ENUM_ENTRY(Error, WGPULogLevel_Error)
	ENUM_ENTRY(Warn, WGPULogLevel_Warn)
	ENUM_ENTRY(Info, WGPULogLevel_Info)
	ENUM_ENTRY(Debug, WGPULogLevel_Debug)
	ENUM_ENTRY(Trace, WGPULogLevel_Trace)
	ENUM_ENTRY(Force32, WGPULogLevel_Force32)
END
ENUM(InstanceBackend)
	ENUM_ENTRY(All, 0x00000000)
	ENUM_ENTRY(Force32, 0x7FFFFFFF)
END
ENUM(InstanceFlag)
	ENUM_ENTRY(Default, 0x00000000)
	ENUM_ENTRY(Force32, 0x7FFFFFFF)
END
ENUM(Dx12Compiler)
	ENUM_ENTRY(Undefined, WGPUDx12Compiler_Undefined)
	ENUM_ENTRY(Fxc, WGPUDx12Compiler_Fxc)
	ENUM_ENTRY(Dxc, WGPUDx12Compiler_Dxc)
	ENUM_ENTRY(Force32, WGPUDx12Compiler_Force32)
END
ENUM(Gles3MinorVersion)
	ENUM_ENTRY(Automatic, WGPUGles3MinorVersion_Automatic)
	ENUM_ENTRY(Version0, WGPUGles3MinorVersion_Version0)
	ENUM_ENTRY(Version1, WGPUGles3MinorVersion_Version1)
	ENUM_ENTRY(Version2, WGPUGles3MinorVersion_Version2)
	ENUM_ENTRY(Force32, WGPUGles3MinorVersion_Force32)
END
ENUM(PipelineStatisticName)
	ENUM_ENTRY(VertexShaderInvocations, WGPUPipelineStatisticName_VertexShaderInvocations)
	ENUM_ENTRY(ClipperInvocations, WGPUPipelineStatisticName_ClipperInvocations)
	ENUM_ENTRY(ClipperPrimitivesOut, WGPUPipelineStatisticName_ClipperPrimitivesOut)
	ENUM_ENTRY(FragmentShaderInvocations, WGPUPipelineStatisticName_FragmentShaderInvocations)
	ENUM_ENTRY(ComputeShaderInvocations, WGPUPipelineStatisticName_ComputeShaderInvocations)
	ENUM_ENTRY(Force32, WGPUPipelineStatisticName_Force32)
END
ENUM(NativeQueryType)
	ENUM_ENTRY(PipelineStatistics, WGPUNativeQueryType_PipelineStatistics)
	ENUM_ENTRY(Force32, WGPUNativeQueryType_Force32)
END
ENUM(NativeTextureFormat)
	ENUM_ENTRY(R16Unorm, WGPUNativeTextureFormat_R16Unorm)
	ENUM_ENTRY(R16Snorm, WGPUNativeTextureFormat_R16Snorm)
	ENUM_ENTRY(Rg16Unorm, WGPUNativeTextureFormat_Rg16Unorm)
	ENUM_ENTRY(Rg16Snorm, WGPUNativeTextureFormat_Rg16Snorm)
	ENUM_ENTRY(Rgba16Unorm, WGPUNativeTextureFormat_Rgba16Unorm)
	ENUM_ENTRY(Rgba16Snorm, WGPUNativeTextureFormat_Rgba16Snorm)
	ENUM_ENTRY(NV12, WGPUNativeTextureFormat_NV12)
END

// Structs
STRUCT_NO_OSTREAM(StringView)
	void setDefault();
	StringView(const std::string_view& cpp) : WGPUStringView{ cpp.data(), cpp.length() } {}
	operator std::string_view() const;
	friend auto operator<<(std::ostream& stream, const S& self) -> std::ostream& {
		return stream << std::string_view(self);
	}
END

STRUCT(ChainedStruct)
	void setDefault();
END

STRUCT(ChainedStructOut)
	void setDefault();
END

STRUCT(BlendComponent)
	void setDefault();
END

STRUCT(Color)
	void setDefault();
	Color(double r, double g, double b, double a) : WGPUColor{ r, g, b, a } {}
END

STRUCT(ComputePassTimestampWrites)
	void setDefault();
END

STRUCT(Extent3D)
	void setDefault();
	Extent3D(uint32_t width, uint32_t height, uint32_t depthOrArrayLayers) : WGPUExtent3D{ width, height, depthOrArrayLayers } {}
END

STRUCT(Future)
	void setDefault();
END

STRUCT(Origin3D)
	void setDefault();
	Origin3D(uint32_t x, uint32_t y, uint32_t z) : WGPUOrigin3D{ x, y, z } {}
END

STRUCT(RenderPassDepthStencilAttachment)
	void setDefault();
END

STRUCT(RenderPassMaxDrawCount)
	void setDefault();
END

STRUCT(RenderPassTimestampWrites)
	void setDefault();
END

STRUCT(ShaderSourceSPIRV)
	void setDefault();
END

STRUCT(ShaderSourceWGSL)
	void setDefault();
END

STRUCT(StencilFaceState)
	void setDefault();
END

STRUCT(SupportedFeatures)
	void setDefault();
	void freeMembers();
END

STRUCT(SupportedWGSLLanguageFeatures)
	void setDefault();
	void freeMembers();
END

STRUCT(SurfaceSourceAndroidNativeWindow)
	void setDefault();
END

STRUCT(SurfaceSourceMetalLayer)
	void setDefault();
END

STRUCT(SurfaceSourceWaylandSurface)
	void setDefault();
END

STRUCT(SurfaceSourceWindowsHWND)
	void setDefault();
END

STRUCT(SurfaceSourceXCBWindow)
	void setDefault();
END

STRUCT(SurfaceSourceXlibWindow)
	void setDefault();
END

STRUCT(TexelCopyBufferLayout)
	void setDefault();
END

STRUCT(VertexAttribute)
	void setDefault();
END

STRUCT(BlendState)
	void setDefault();
END

STRUCT(FutureWaitInfo)
	void setDefault();
END

STRUCT(TexelCopyBufferInfo)
	void setDefault();
END

STRUCT(TexelCopyTextureInfo)
	void setDefault();
END

STRUCT(VertexBufferLayout)
	void setDefault();
END

STRUCT(InstanceExtras)
	void setDefault();
END

STRUCT(DeviceExtras)
	void setDefault();
END

STRUCT(NativeLimits)
	void setDefault();
END

STRUCT(PushConstantRange)
	void setDefault();
END

STRUCT(PipelineLayoutExtras)
	void setDefault();
END

STRUCT(ShaderDefine)
	void setDefault();
END

STRUCT(ShaderModuleGLSLDescriptor)
	void setDefault();
END

STRUCT(ShaderModuleDescriptorSpirV)
	void setDefault();
END

STRUCT(RegistryReport)
	void setDefault();
END

STRUCT(HubReport)
	void setDefault();
END

STRUCT(GlobalReport)
	void setDefault();
END

STRUCT(BindGroupEntryExtras)
	void setDefault();
END

STRUCT(BindGroupLayoutEntryExtras)
	void setDefault();
END

STRUCT(QuerySetDescriptorExtras)
	void setDefault();
END

STRUCT(SurfaceConfigurationExtras)
	void setDefault();
END


// Descriptors
DESCRIPTOR(BufferMapCallbackInfo)
	void setDefault();
END

DESCRIPTOR(CompilationInfoCallbackInfo)
	void setDefault();
END

DESCRIPTOR(CreateComputePipelineAsyncCallbackInfo)
	void setDefault();
END

DESCRIPTOR(CreateRenderPipelineAsyncCallbackInfo)
	void setDefault();
END

DESCRIPTOR(DeviceLostCallbackInfo)
	void setDefault();
END

DESCRIPTOR(PopErrorScopeCallbackInfo)
	void setDefault();
END

DESCRIPTOR(QueueWorkDoneCallbackInfo)
	void setDefault();
END

DESCRIPTOR(RequestAdapterCallbackInfo)
	void setDefault();
END

DESCRIPTOR(RequestDeviceCallbackInfo)
	void setDefault();
END

DESCRIPTOR(UncapturedErrorCallbackInfo)
	void setDefault();
END

DESCRIPTOR(AdapterInfo)
	void setDefault();
	void freeMembers();
END

DESCRIPTOR(BindGroupEntry)
	void setDefault();
END

DESCRIPTOR(BufferBindingLayout)
	void setDefault();
END

DESCRIPTOR(BufferDescriptor)
	void setDefault();
END

DESCRIPTOR(CommandBufferDescriptor)
	void setDefault();
END

DESCRIPTOR(CommandEncoderDescriptor)
	void setDefault();
END

DESCRIPTOR(CompilationMessage)
	void setDefault();
END

DESCRIPTOR(ConstantEntry)
	void setDefault();
END

DESCRIPTOR(InstanceCapabilities)
	void setDefault();
END

DESCRIPTOR(Limits)
	void setDefault();
END

DESCRIPTOR(MultisampleState)
	void setDefault();
END

DESCRIPTOR(PipelineLayoutDescriptor)
	void setDefault();
END

DESCRIPTOR(PrimitiveState)
	void setDefault();
END

DESCRIPTOR(QuerySetDescriptor)
	void setDefault();
END

DESCRIPTOR(QueueDescriptor)
	void setDefault();
END

DESCRIPTOR(RenderBundleDescriptor)
	void setDefault();
END

DESCRIPTOR(RenderBundleEncoderDescriptor)
	void setDefault();
END

DESCRIPTOR(RequestAdapterOptions)
	void setDefault();
END

DESCRIPTOR(SamplerBindingLayout)
	void setDefault();
END

DESCRIPTOR(SamplerDescriptor)
	void setDefault();
END

DESCRIPTOR(ShaderModuleDescriptor)
	void setDefault();
END

DESCRIPTOR(StorageTextureBindingLayout)
	void setDefault();
END

DESCRIPTOR(SurfaceCapabilities)
	void setDefault();
	void freeMembers();
END

DESCRIPTOR(SurfaceConfiguration)
	void setDefault();
END

DESCRIPTOR(SurfaceDescriptor)
	void setDefault();
END

DESCRIPTOR(SurfaceTexture)
	void setDefault();
END

DESCRIPTOR(TextureBindingLayout)
	void setDefault();
END

DESCRIPTOR(TextureViewDescriptor)
	void setDefault();
END

DESCRIPTOR(BindGroupDescriptor)
	void setDefault();
END

DESCRIPTOR(BindGroupLayoutEntry)
	void setDefault();
END

DESCRIPTOR(CompilationInfo)
	void setDefault();
END

DESCRIPTOR(ComputePassDescriptor)
	void setDefault();
END

DESCRIPTOR(DepthStencilState)
	void setDefault();
END

DESCRIPTOR(DeviceDescriptor)
	void setDefault();
END

DESCRIPTOR(InstanceDescriptor)
	void setDefault();
END

DESCRIPTOR(ProgrammableStageDescriptor)
	void setDefault();
END

DESCRIPTOR(RenderPassColorAttachment)
	void setDefault();
END

DESCRIPTOR(TextureDescriptor)
	void setDefault();
END

DESCRIPTOR(BindGroupLayoutDescriptor)
	void setDefault();
END

DESCRIPTOR(ColorTargetState)
	void setDefault();
END

DESCRIPTOR(ComputePipelineDescriptor)
	void setDefault();
END

DESCRIPTOR(RenderPassDescriptor)
	void setDefault();
END

DESCRIPTOR(VertexState)
	void setDefault();
END

DESCRIPTOR(FragmentState)
	void setDefault();
END

DESCRIPTOR(RenderPipelineDescriptor)
	void setDefault();
END

DESCRIPTOR(InstanceEnumerateAdapterOptions)
	void setDefault();
END


// Handles forward declarations
class Adapter;
class BindGroup;
class BindGroupLayout;
class Buffer;
class CommandBuffer;
class CommandEncoder;
class ComputePassEncoder;
class ComputePipeline;
class Device;
class Instance;
class PipelineLayout;
class QuerySet;
class Queue;
class RenderBundle;
class RenderBundleEncoder;
class RenderPassEncoder;
class RenderPipeline;
class Sampler;
class ShaderModule;
class Surface;
class Texture;
class TextureView;

// Callback types
using BufferMapCallback = std::function<void(MapAsyncStatus status, StringView message, void* userdata1)>;
using CompilationInfoCallback = std::function<void(CompilationInfoRequestStatus status, const CompilationInfo& compilationInfo, void* userdata1)>;
using CreateComputePipelineAsyncCallback = std::function<void(CreatePipelineAsyncStatus status, ComputePipeline pipeline, StringView message, void* userdata1)>;
using CreateRenderPipelineAsyncCallback = std::function<void(CreatePipelineAsyncStatus status, RenderPipeline pipeline, StringView message, void* userdata1)>;
using DeviceLostCallback = std::function<void(Device const * device, DeviceLostReason reason, StringView message, void* userdata1)>;
using PopErrorScopeCallback = std::function<void(PopErrorScopeStatus status, ErrorType type, StringView message, void* userdata1)>;
using QueueWorkDoneCallback = std::function<void(QueueWorkDoneStatus status, void* userdata1)>;
using RequestAdapterCallback = std::function<void(RequestAdapterStatus status, Adapter adapter, StringView message, void* userdata1)>;
using RequestDeviceCallback = std::function<void(RequestDeviceStatus status, Device device, StringView message, void* userdata1)>;
using UncapturedErrorCallback = std::function<void(Device const * device, ErrorType type, StringView message, void* userdata1)>;
using LogCallback = std::function<void(LogLevel level, StringView message)>;

// Handles detailed declarations
HANDLE(Adapter)
	void getFeatures(SupportedFeatures * features);
	Status getInfo(AdapterInfo * info);
	Status getLimits(Limits * limits);
	Bool hasFeature(FeatureName feature);
	Future requestDevice(const DeviceDescriptor& descriptor, RequestDeviceCallbackInfo callbackInfo);
	void addRef();
	void release();
	Device requestDevice(const DeviceDescriptor& descriptor);
END

HANDLE(BindGroup)
	void setLabel(StringView label);
	void addRef();
	void release();
END

HANDLE(BindGroupLayout)
	void setLabel(StringView label);
	void addRef();
	void release();
END

HANDLE(Buffer)
	void destroy();
	void const * getConstMappedRange(size_t offset, size_t size);
	BufferMapState getMapState();
	void * getMappedRange(size_t offset, size_t size);
	uint64_t getSize();
	BufferUsage getUsage();
	Future mapAsync(MapMode mode, size_t offset, size_t size, BufferMapCallbackInfo callbackInfo);
	void setLabel(StringView label);
	void unmap();
	void addRef();
	void release();
END

HANDLE(CommandBuffer)
	void setLabel(StringView label);
	void addRef();
	void release();
END

HANDLE(CommandEncoder)
	ComputePassEncoder beginComputePass(const ComputePassDescriptor& descriptor);
	ComputePassEncoder beginComputePass();
	RenderPassEncoder beginRenderPass(const RenderPassDescriptor& descriptor);
	void clearBuffer(Buffer buffer, uint64_t offset, uint64_t size);
	void copyBufferToBuffer(Buffer source, uint64_t sourceOffset, Buffer destination, uint64_t destinationOffset, uint64_t size);
	void copyBufferToTexture(const TexelCopyBufferInfo& source, const TexelCopyTextureInfo& destination, const Extent3D& copySize);
	void copyTextureToBuffer(const TexelCopyTextureInfo& source, const TexelCopyBufferInfo& destination, const Extent3D& copySize);
	void copyTextureToTexture(const TexelCopyTextureInfo& source, const TexelCopyTextureInfo& destination, const Extent3D& copySize);
	CommandBuffer finish(const CommandBufferDescriptor& descriptor);
	CommandBuffer finish();
	void insertDebugMarker(StringView markerLabel);
	void popDebugGroup();
	void pushDebugGroup(StringView groupLabel);
	void resolveQuerySet(QuerySet querySet, uint32_t firstQuery, uint32_t queryCount, Buffer destination, uint64_t destinationOffset);
	void setLabel(StringView label);
	void writeTimestamp(QuerySet querySet, uint32_t queryIndex);
	void addRef();
	void release();
END

HANDLE(ComputePassEncoder)
	void dispatchWorkgroups(uint32_t workgroupCountX, uint32_t workgroupCountY, uint32_t workgroupCountZ);
	void dispatchWorkgroupsIndirect(Buffer indirectBuffer, uint64_t indirectOffset);
	void end();
	void insertDebugMarker(StringView markerLabel);
	void popDebugGroup();
	void pushDebugGroup(StringView groupLabel);
	void setBindGroup(uint32_t groupIndex, BindGroup group, size_t dynamicOffsetCount, uint32_t const * dynamicOffsets);
	void setBindGroup(uint32_t groupIndex, BindGroup group, const std::vector<uint32_t>& dynamicOffsets);
	void setBindGroup(uint32_t groupIndex, BindGroup group, const uint32_t& dynamicOffsets);
	void setLabel(StringView label);
	void setPipeline(ComputePipeline pipeline);
	void addRef();
	void release();
	void setPushConstants(uint32_t offset, uint32_t sizeBytes, void const * data);
	void beginPipelineStatisticsQuery(QuerySet querySet, uint32_t queryIndex);
	void endPipelineStatisticsQuery();
	void writeTimestamp(QuerySet querySet, uint32_t queryIndex);
END

HANDLE(ComputePipeline)
	BindGroupLayout getBindGroupLayout(uint32_t groupIndex);
	void setLabel(StringView label);
	void addRef();
	void release();
END

HANDLE(Device)
	BindGroup createBindGroup(const BindGroupDescriptor& descriptor);
	BindGroupLayout createBindGroupLayout(const BindGroupLayoutDescriptor& descriptor);
	Buffer createBuffer(const BufferDescriptor& descriptor);
	CommandEncoder createCommandEncoder(const CommandEncoderDescriptor& descriptor);
	CommandEncoder createCommandEncoder();
	ComputePipeline createComputePipeline(const ComputePipelineDescriptor& descriptor);
	Future createComputePipelineAsync(const ComputePipelineDescriptor& descriptor, CreateComputePipelineAsyncCallbackInfo callbackInfo);
	PipelineLayout createPipelineLayout(const PipelineLayoutDescriptor& descriptor);
	QuerySet createQuerySet(const QuerySetDescriptor& descriptor);
	RenderBundleEncoder createRenderBundleEncoder(const RenderBundleEncoderDescriptor& descriptor);
	RenderPipeline createRenderPipeline(const RenderPipelineDescriptor& descriptor);
	Future createRenderPipelineAsync(const RenderPipelineDescriptor& descriptor, CreateRenderPipelineAsyncCallbackInfo callbackInfo);
	Sampler createSampler(const SamplerDescriptor& descriptor);
	Sampler createSampler();
	ShaderModule createShaderModule(const ShaderModuleDescriptor& descriptor);
	Texture createTexture(const TextureDescriptor& descriptor);
	void destroy();
	AdapterInfo getAdapterInfo();
	void getFeatures(SupportedFeatures * features);
	Status getLimits(Limits * limits);
	Future getLostFuture();
	Queue getQueue();
	Bool hasFeature(FeatureName feature);
	Future popErrorScope(PopErrorScopeCallbackInfo callbackInfo);
	void pushErrorScope(ErrorFilter filter);
	void setLabel(StringView label);
	void addRef();
	void release();
	Bool poll(Bool wait, SubmissionIndex const * wrappedSubmissionIndex);
	ShaderModule createShaderModuleSpirV(const ShaderModuleDescriptorSpirV& descriptor);
END

HANDLE(Instance)
	Surface createSurface(const SurfaceDescriptor& descriptor);
	Status getWGSLLanguageFeatures(SupportedWGSLLanguageFeatures * features);
	Bool hasWGSLLanguageFeature(WGSLLanguageFeatureName feature);
	void processEvents();
	Future requestAdapter(const RequestAdapterOptions& options, RequestAdapterCallbackInfo callbackInfo);
	WaitStatus waitAny(size_t futureCount, FutureWaitInfo * futures, uint64_t timeoutNS);
	void addRef();
	void release();
	size_t enumerateAdapters(const InstanceEnumerateAdapterOptions& options, Adapter * adapters);
	Adapter requestAdapter(const RequestAdapterOptions& options);
END

HANDLE(PipelineLayout)
	void setLabel(StringView label);
	void addRef();
	void release();
END

HANDLE(QuerySet)
	void destroy();
	uint32_t getCount();
	QueryType getType();
	void setLabel(StringView label);
	void addRef();
	void release();
END

HANDLE(Queue)
	Future onSubmittedWorkDone(QueueWorkDoneCallbackInfo callbackInfo);
	void setLabel(StringView label);
	void submit(size_t commandCount, CommandBuffer const * commands);
	void submit(const std::vector<WGPUCommandBuffer>& commands);
	void submit(const WGPUCommandBuffer& commands);
	void writeBuffer(Buffer buffer, uint64_t bufferOffset, void const * data, size_t size);
	void writeTexture(const TexelCopyTextureInfo& destination, void const * data, size_t dataSize, const TexelCopyBufferLayout& dataLayout, const Extent3D& writeSize);
	void addRef();
	void release();
	SubmissionIndex submitForIndex(size_t commandCount, CommandBuffer const * commands);
	SubmissionIndex submitForIndex(const std::vector<WGPUCommandBuffer>& commands);
	SubmissionIndex submitForIndex(const WGPUCommandBuffer& commands);
END

HANDLE(RenderBundle)
	void setLabel(StringView label);
	void addRef();
	void release();
END

HANDLE(RenderBundleEncoder)
	void draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance);
	void drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t baseVertex, uint32_t firstInstance);
	void drawIndexedIndirect(Buffer indirectBuffer, uint64_t indirectOffset);
	void drawIndirect(Buffer indirectBuffer, uint64_t indirectOffset);
	RenderBundle finish(const RenderBundleDescriptor& descriptor);
	RenderBundle finish();
	void insertDebugMarker(StringView markerLabel);
	void popDebugGroup();
	void pushDebugGroup(StringView groupLabel);
	void setBindGroup(uint32_t groupIndex, BindGroup group, size_t dynamicOffsetCount, uint32_t const * dynamicOffsets);
	void setBindGroup(uint32_t groupIndex, BindGroup group, const std::vector<uint32_t>& dynamicOffsets);
	void setBindGroup(uint32_t groupIndex, BindGroup group, const uint32_t& dynamicOffsets);
	void setIndexBuffer(Buffer buffer, IndexFormat format, uint64_t offset, uint64_t size);
	void setLabel(StringView label);
	void setPipeline(RenderPipeline pipeline);
	void setVertexBuffer(uint32_t slot, Buffer buffer, uint64_t offset, uint64_t size);
	void addRef();
	void release();
END

HANDLE(RenderPassEncoder)
	void beginOcclusionQuery(uint32_t queryIndex);
	void draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance);
	void drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t baseVertex, uint32_t firstInstance);
	void drawIndexedIndirect(Buffer indirectBuffer, uint64_t indirectOffset);
	void drawIndirect(Buffer indirectBuffer, uint64_t indirectOffset);
	void end();
	void endOcclusionQuery();
	void executeBundles(size_t bundleCount, RenderBundle const * bundles);
	void executeBundles(const std::vector<WGPURenderBundle>& bundles);
	void executeBundles(const WGPURenderBundle& bundles);
	void insertDebugMarker(StringView markerLabel);
	void popDebugGroup();
	void pushDebugGroup(StringView groupLabel);
	void setBindGroup(uint32_t groupIndex, BindGroup group, size_t dynamicOffsetCount, uint32_t const * dynamicOffsets);
	void setBindGroup(uint32_t groupIndex, BindGroup group, const std::vector<uint32_t>& dynamicOffsets);
	void setBindGroup(uint32_t groupIndex, BindGroup group, const uint32_t& dynamicOffsets);
	void setBlendConstant(const Color& color);
	void setIndexBuffer(Buffer buffer, IndexFormat format, uint64_t offset, uint64_t size);
	void setLabel(StringView label);
	void setPipeline(RenderPipeline pipeline);
	void setScissorRect(uint32_t x, uint32_t y, uint32_t width, uint32_t height);
	void setStencilReference(uint32_t reference);
	void setVertexBuffer(uint32_t slot, Buffer buffer, uint64_t offset, uint64_t size);
	void setViewport(float x, float y, float width, float height, float minDepth, float maxDepth);
	void addRef();
	void release();
	void setPushConstants(ShaderStage stages, uint32_t offset, uint32_t sizeBytes, void const * data);
	void multiDrawIndirect(Buffer buffer, uint64_t offset, uint32_t count);
	void multiDrawIndexedIndirect(Buffer buffer, uint64_t offset, uint32_t count);
	void multiDrawIndirectCount(Buffer buffer, uint64_t offset, Buffer count_buffer, uint64_t count_buffer_offset, uint32_t max_count);
	void multiDrawIndexedIndirectCount(Buffer buffer, uint64_t offset, Buffer count_buffer, uint64_t count_buffer_offset, uint32_t max_count);
	void beginPipelineStatisticsQuery(QuerySet querySet, uint32_t queryIndex);
	void endPipelineStatisticsQuery();
	void writeTimestamp(QuerySet querySet, uint32_t queryIndex);
END

HANDLE(RenderPipeline)
	BindGroupLayout getBindGroupLayout(uint32_t groupIndex);
	void setLabel(StringView label);
	void addRef();
	void release();
END

HANDLE(Sampler)
	void setLabel(StringView label);
	void addRef();
	void release();
END

HANDLE(ShaderModule)
	Future getCompilationInfo(CompilationInfoCallbackInfo callbackInfo);
	void setLabel(StringView label);
	void addRef();
	void release();
END

HANDLE(Surface)
	void configure(const SurfaceConfiguration& config);
	Status getCapabilities(Adapter adapter, SurfaceCapabilities * capabilities);
	void getCurrentTexture(SurfaceTexture * surfaceTexture);
	Status present();
	void setLabel(StringView label);
	void unconfigure();
	void addRef();
	void release();
END

HANDLE(Texture)
	TextureView createView(const TextureViewDescriptor& descriptor);
	TextureView createView();
	void destroy();
	uint32_t getDepthOrArrayLayers();
	TextureDimension getDimension();
	TextureFormat getFormat();
	uint32_t getHeight();
	uint32_t getMipLevelCount();
	uint32_t getSampleCount();
	TextureUsage getUsage();
	uint32_t getWidth();
	void setLabel(StringView label);
	void addRef();
	void release();
END

HANDLE(TextureView)
	void setLabel(StringView label);
	void addRef();
	void release();
END


// Non-member procedures


Instance createInstance();
Instance createInstance(const InstanceDescriptor& descriptor);

#ifdef WEBGPU_CPP_IMPLEMENTATION

Instance createInstance() {
	return wgpuCreateInstance(nullptr);
}

Instance createInstance(const InstanceDescriptor& descriptor) {
	return wgpuCreateInstance(&descriptor);
}

// Handles members implementation
// Methods of StringView
void StringView::setDefault() {
}


// Methods of ChainedStruct
void ChainedStruct::setDefault() {
}


// Methods of ChainedStructOut
void ChainedStructOut::setDefault() {
}


// Methods of BufferMapCallbackInfo
void BufferMapCallbackInfo::setDefault() {
}


// Methods of CompilationInfoCallbackInfo
void CompilationInfoCallbackInfo::setDefault() {
}


// Methods of CreateComputePipelineAsyncCallbackInfo
void CreateComputePipelineAsyncCallbackInfo::setDefault() {
}


// Methods of CreateRenderPipelineAsyncCallbackInfo
void CreateRenderPipelineAsyncCallbackInfo::setDefault() {
}


// Methods of DeviceLostCallbackInfo
void DeviceLostCallbackInfo::setDefault() {
}


// Methods of PopErrorScopeCallbackInfo
void PopErrorScopeCallbackInfo::setDefault() {
}


// Methods of QueueWorkDoneCallbackInfo
void QueueWorkDoneCallbackInfo::setDefault() {
}


// Methods of RequestAdapterCallbackInfo
void RequestAdapterCallbackInfo::setDefault() {
}


// Methods of RequestDeviceCallbackInfo
void RequestDeviceCallbackInfo::setDefault() {
}


// Methods of UncapturedErrorCallbackInfo
void UncapturedErrorCallbackInfo::setDefault() {
}


// Methods of AdapterInfo
void AdapterInfo::setDefault() {
	backendType = BackendType::Undefined;
	((StringView*)&vendor)->setDefault();
	((StringView*)&architecture)->setDefault();
	((StringView*)&device)->setDefault();
	((StringView*)&description)->setDefault();
}
void AdapterInfo::freeMembers() {
	return wgpuAdapterInfoFreeMembers(*this);
}


// Methods of BindGroupEntry
void BindGroupEntry::setDefault() {
	offset = 0;
}


// Methods of BlendComponent
void BlendComponent::setDefault() {
	operation = BlendOperation::Add;
	srcFactor = BlendFactor::One;
	dstFactor = BlendFactor::Zero;
}


// Methods of BufferBindingLayout
void BufferBindingLayout::setDefault() {
	type = BufferBindingType::Uniform;
	hasDynamicOffset = false;
	minBindingSize = 0;
}


// Methods of BufferDescriptor
void BufferDescriptor::setDefault() {
	mappedAtCreation = false;
	((StringView*)&label)->setDefault();
}


// Methods of Color
void Color::setDefault() {
}


// Methods of CommandBufferDescriptor
void CommandBufferDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
}


// Methods of CommandEncoderDescriptor
void CommandEncoderDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
}


// Methods of CompilationMessage
void CompilationMessage::setDefault() {
	((StringView*)&message)->setDefault();
}


// Methods of ComputePassTimestampWrites
void ComputePassTimestampWrites::setDefault() {
}


// Methods of ConstantEntry
void ConstantEntry::setDefault() {
	((StringView*)&key)->setDefault();
}


// Methods of Extent3D
void Extent3D::setDefault() {
	height = 1;
	depthOrArrayLayers = 1;
}


// Methods of Future
void Future::setDefault() {
}


// Methods of InstanceCapabilities
void InstanceCapabilities::setDefault() {
}


// Methods of Limits
void Limits::setDefault() {
	maxTextureDimension1D = WGPU_LIMIT_U32_UNDEFINED;
	maxTextureDimension2D = WGPU_LIMIT_U32_UNDEFINED;
	maxTextureDimension3D = WGPU_LIMIT_U32_UNDEFINED;
	maxTextureArrayLayers = WGPU_LIMIT_U32_UNDEFINED;
	maxBindGroups = WGPU_LIMIT_U32_UNDEFINED;
	maxBindGroupsPlusVertexBuffers = WGPU_LIMIT_U32_UNDEFINED;
	maxBindingsPerBindGroup = WGPU_LIMIT_U32_UNDEFINED;
	maxDynamicUniformBuffersPerPipelineLayout = WGPU_LIMIT_U32_UNDEFINED;
	maxDynamicStorageBuffersPerPipelineLayout = WGPU_LIMIT_U32_UNDEFINED;
	maxSampledTexturesPerShaderStage = WGPU_LIMIT_U32_UNDEFINED;
	maxSamplersPerShaderStage = WGPU_LIMIT_U32_UNDEFINED;
	maxStorageBuffersPerShaderStage = WGPU_LIMIT_U32_UNDEFINED;
	maxStorageTexturesPerShaderStage = WGPU_LIMIT_U32_UNDEFINED;
	maxUniformBuffersPerShaderStage = WGPU_LIMIT_U32_UNDEFINED;
	maxUniformBufferBindingSize = WGPU_LIMIT_U64_UNDEFINED;
	maxStorageBufferBindingSize = WGPU_LIMIT_U64_UNDEFINED;
	minUniformBufferOffsetAlignment = WGPU_LIMIT_U32_UNDEFINED;
	minStorageBufferOffsetAlignment = WGPU_LIMIT_U32_UNDEFINED;
	maxVertexBuffers = WGPU_LIMIT_U32_UNDEFINED;
	maxBufferSize = WGPU_LIMIT_U64_UNDEFINED;
	maxVertexAttributes = WGPU_LIMIT_U32_UNDEFINED;
	maxVertexBufferArrayStride = WGPU_LIMIT_U32_UNDEFINED;
	maxInterStageShaderVariables = WGPU_LIMIT_U32_UNDEFINED;
	maxColorAttachments = WGPU_LIMIT_U32_UNDEFINED;
	maxColorAttachmentBytesPerSample = WGPU_LIMIT_U32_UNDEFINED;
	maxComputeWorkgroupStorageSize = WGPU_LIMIT_U32_UNDEFINED;
	maxComputeInvocationsPerWorkgroup = WGPU_LIMIT_U32_UNDEFINED;
	maxComputeWorkgroupSizeX = WGPU_LIMIT_U32_UNDEFINED;
	maxComputeWorkgroupSizeY = WGPU_LIMIT_U32_UNDEFINED;
	maxComputeWorkgroupSizeZ = WGPU_LIMIT_U32_UNDEFINED;
	maxComputeWorkgroupsPerDimension = WGPU_LIMIT_U32_UNDEFINED;
}


// Methods of MultisampleState
void MultisampleState::setDefault() {
	count = 1;
	mask = 0xFFFFFFFF;
	alphaToCoverageEnabled = false;
}


// Methods of Origin3D
void Origin3D::setDefault() {
	x = 0;
	y = 0;
	z = 0;
}


// Methods of PipelineLayoutDescriptor
void PipelineLayoutDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
}


// Methods of PrimitiveState
void PrimitiveState::setDefault() {
	topology = PrimitiveTopology::TriangleList;
	stripIndexFormat = IndexFormat::Undefined;
	frontFace = FrontFace::CCW;
	cullMode = CullMode::None;
}


// Methods of QuerySetDescriptor
void QuerySetDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
}


// Methods of QueueDescriptor
void QueueDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
}


// Methods of RenderBundleDescriptor
void RenderBundleDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
}


// Methods of RenderBundleEncoderDescriptor
void RenderBundleEncoderDescriptor::setDefault() {
	depthStencilFormat = TextureFormat::Undefined;
	depthReadOnly = false;
	stencilReadOnly = false;
	sampleCount = 1;
	((StringView*)&label)->setDefault();
}


// Methods of RenderPassDepthStencilAttachment
void RenderPassDepthStencilAttachment::setDefault() {
	depthLoadOp = LoadOp::Undefined;
	depthStoreOp = StoreOp::Undefined;
	depthReadOnly = false;
	stencilLoadOp = LoadOp::Undefined;
	stencilStoreOp = StoreOp::Undefined;
	stencilClearValue = 0;
	stencilReadOnly = false;
}


// Methods of RenderPassMaxDrawCount
void RenderPassMaxDrawCount::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	chain.sType = SType::RenderPassMaxDrawCount;
}


// Methods of RenderPassTimestampWrites
void RenderPassTimestampWrites::setDefault() {
}


// Methods of RequestAdapterOptions
void RequestAdapterOptions::setDefault() {
	powerPreference = PowerPreference::Undefined;
	forceFallbackAdapter = false;
	backendType = BackendType::Undefined;
}


// Methods of SamplerBindingLayout
void SamplerBindingLayout::setDefault() {
	type = SamplerBindingType::Filtering;
}


// Methods of SamplerDescriptor
void SamplerDescriptor::setDefault() {
	addressModeU = AddressMode::ClampToEdge;
	addressModeV = AddressMode::ClampToEdge;
	addressModeW = AddressMode::ClampToEdge;
	magFilter = FilterMode::Nearest;
	minFilter = FilterMode::Nearest;
	mipmapFilter = MipmapFilterMode::Nearest;
	lodMinClamp = 0;
	lodMaxClamp = 32;
	compare = CompareFunction::Undefined;
	((StringView*)&label)->setDefault();
}


// Methods of ShaderModuleDescriptor
void ShaderModuleDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
}


// Methods of ShaderSourceSPIRV
void ShaderSourceSPIRV::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	chain.sType = SType::ShaderSourceSPIRV;
}


// Methods of ShaderSourceWGSL
void ShaderSourceWGSL::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	((StringView*)&code)->setDefault();
	chain.sType = SType::ShaderSourceWGSL;
}


// Methods of StencilFaceState
void StencilFaceState::setDefault() {
	compare = CompareFunction::Always;
	failOp = StencilOperation::Keep;
	depthFailOp = StencilOperation::Keep;
	passOp = StencilOperation::Keep;
}


// Methods of StorageTextureBindingLayout
void StorageTextureBindingLayout::setDefault() {
	access = StorageTextureAccess::WriteOnly;
	format = TextureFormat::Undefined;
	viewDimension = TextureViewDimension::_2D;
}


// Methods of SupportedFeatures
void SupportedFeatures::setDefault() {
}
void SupportedFeatures::freeMembers() {
	return wgpuSupportedFeaturesFreeMembers(*this);
}


// Methods of SupportedWGSLLanguageFeatures
void SupportedWGSLLanguageFeatures::setDefault() {
}
void SupportedWGSLLanguageFeatures::freeMembers() {
	return wgpuSupportedWGSLLanguageFeaturesFreeMembers(*this);
}


// Methods of SurfaceCapabilities
void SurfaceCapabilities::setDefault() {
}
void SurfaceCapabilities::freeMembers() {
	return wgpuSurfaceCapabilitiesFreeMembers(*this);
}


// Methods of SurfaceConfiguration
void SurfaceConfiguration::setDefault() {
	format = TextureFormat::Undefined;
	presentMode = PresentMode::Undefined;
}


// Methods of SurfaceDescriptor
void SurfaceDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
}


// Methods of SurfaceSourceAndroidNativeWindow
void SurfaceSourceAndroidNativeWindow::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	chain.sType = SType::SurfaceSourceAndroidNativeWindow;
}


// Methods of SurfaceSourceMetalLayer
void SurfaceSourceMetalLayer::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	chain.sType = SType::SurfaceSourceMetalLayer;
}


// Methods of SurfaceSourceWaylandSurface
void SurfaceSourceWaylandSurface::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	chain.sType = SType::SurfaceSourceWaylandSurface;
}


// Methods of SurfaceSourceWindowsHWND
void SurfaceSourceWindowsHWND::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	chain.sType = SType::SurfaceSourceWindowsHWND;
}


// Methods of SurfaceSourceXCBWindow
void SurfaceSourceXCBWindow::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	chain.sType = SType::SurfaceSourceXCBWindow;
}


// Methods of SurfaceSourceXlibWindow
void SurfaceSourceXlibWindow::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	chain.sType = SType::SurfaceSourceXlibWindow;
}


// Methods of SurfaceTexture
void SurfaceTexture::setDefault() {
}


// Methods of TexelCopyBufferLayout
void TexelCopyBufferLayout::setDefault() {
}


// Methods of TextureBindingLayout
void TextureBindingLayout::setDefault() {
	sampleType = TextureSampleType::Float;
	viewDimension = TextureViewDimension::_2D;
	multisampled = false;
}


// Methods of TextureViewDescriptor
void TextureViewDescriptor::setDefault() {
	format = TextureFormat::Undefined;
	dimension = TextureViewDimension::Undefined;
	baseMipLevel = 0;
	baseArrayLayer = 0;
	aspect = TextureAspect::All;
	((StringView*)&label)->setDefault();
}


// Methods of VertexAttribute
void VertexAttribute::setDefault() {
}


// Methods of BindGroupDescriptor
void BindGroupDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
}


// Methods of BindGroupLayoutEntry
void BindGroupLayoutEntry::setDefault() {
	((BufferBindingLayout*)&buffer)->setDefault();
	((SamplerBindingLayout*)&sampler)->setDefault();
	((TextureBindingLayout*)&texture)->setDefault();
	((StorageTextureBindingLayout*)&storageTexture)->setDefault();
	buffer.type = BufferBindingType::Undefined;
	sampler.type = SamplerBindingType::Undefined;
	storageTexture.access = StorageTextureAccess::Undefined;
	texture.sampleType = TextureSampleType::Undefined;
}


// Methods of BlendState
void BlendState::setDefault() {
	((BlendComponent*)&color)->setDefault();
	((BlendComponent*)&alpha)->setDefault();
}


// Methods of CompilationInfo
void CompilationInfo::setDefault() {
}


// Methods of ComputePassDescriptor
void ComputePassDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
}


// Methods of DepthStencilState
void DepthStencilState::setDefault() {
	format = TextureFormat::Undefined;
	depthWriteEnabled = OptionalBool::Undefined;
	depthCompare = CompareFunction::Undefined;
	stencilReadMask = 0xFFFFFFFF;
	stencilWriteMask = 0xFFFFFFFF;
	depthBias = 0;
	depthBiasSlopeScale = 0;
	depthBiasClamp = 0;
	((StencilFaceState*)&stencilFront)->setDefault();
	((StencilFaceState*)&stencilBack)->setDefault();
}


// Methods of DeviceDescriptor
void DeviceDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
	((QueueDescriptor*)&defaultQueue)->setDefault();
	((DeviceLostCallbackInfo*)&deviceLostCallbackInfo)->setDefault();
	((UncapturedErrorCallbackInfo*)&uncapturedErrorCallbackInfo)->setDefault();
}


// Methods of FutureWaitInfo
void FutureWaitInfo::setDefault() {
	((Future*)&future)->setDefault();
}


// Methods of InstanceDescriptor
void InstanceDescriptor::setDefault() {
	((InstanceCapabilities*)&features)->setDefault();
}


// Methods of ProgrammableStageDescriptor
void ProgrammableStageDescriptor::setDefault() {
	((StringView*)&entryPoint)->setDefault();
}


// Methods of RenderPassColorAttachment
void RenderPassColorAttachment::setDefault() {
	loadOp = LoadOp::Undefined;
	storeOp = StoreOp::Undefined;
	((Color*)&clearValue)->setDefault();
}


// Methods of TexelCopyBufferInfo
void TexelCopyBufferInfo::setDefault() {
	((TexelCopyBufferLayout*)&layout)->setDefault();
}


// Methods of TexelCopyTextureInfo
void TexelCopyTextureInfo::setDefault() {
	aspect = TextureAspect::Undefined;
	((Origin3D*)&origin)->setDefault();
}


// Methods of TextureDescriptor
void TextureDescriptor::setDefault() {
	dimension = TextureDimension::_2D;
	format = TextureFormat::Undefined;
	mipLevelCount = 1;
	sampleCount = 1;
	((StringView*)&label)->setDefault();
	((Extent3D*)&size)->setDefault();
}


// Methods of VertexBufferLayout
void VertexBufferLayout::setDefault() {
	stepMode = VertexStepMode::Vertex;
}


// Methods of BindGroupLayoutDescriptor
void BindGroupLayoutDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
}


// Methods of ColorTargetState
void ColorTargetState::setDefault() {
	format = TextureFormat::Undefined;
}


// Methods of ComputePipelineDescriptor
void ComputePipelineDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
	((ProgrammableStageDescriptor*)&compute)->setDefault();
}


// Methods of RenderPassDescriptor
void RenderPassDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
}


// Methods of VertexState
void VertexState::setDefault() {
	((StringView*)&entryPoint)->setDefault();
}


// Methods of FragmentState
void FragmentState::setDefault() {
	((StringView*)&entryPoint)->setDefault();
}


// Methods of RenderPipelineDescriptor
void RenderPipelineDescriptor::setDefault() {
	((StringView*)&label)->setDefault();
	((VertexState*)&vertex)->setDefault();
	((PrimitiveState*)&primitive)->setDefault();
	((MultisampleState*)&multisample)->setDefault();
}


// Methods of InstanceExtras
void InstanceExtras::setDefault() {
	dx12ShaderCompiler = Dx12Compiler::Undefined;
	((ChainedStruct*)&chain)->setDefault();
	((StringView*)&dxilPath)->setDefault();
	((StringView*)&dxcPath)->setDefault();
	chain.sType = (WGPUSType)NativeSType::InstanceExtras;
}


// Methods of DeviceExtras
void DeviceExtras::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	((StringView*)&tracePath)->setDefault();
	chain.sType = (WGPUSType)NativeSType::DeviceExtras;
}


// Methods of NativeLimits
void NativeLimits::setDefault() {
	((ChainedStructOut*)&chain)->setDefault();
	chain.sType = (WGPUSType)NativeSType::NativeLimits;
}


// Methods of PushConstantRange
void PushConstantRange::setDefault() {
}


// Methods of PipelineLayoutExtras
void PipelineLayoutExtras::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	chain.sType = (WGPUSType)NativeSType::PipelineLayoutExtras;
}


// Methods of ShaderDefine
void ShaderDefine::setDefault() {
	((StringView*)&name)->setDefault();
	((StringView*)&value)->setDefault();
}


// Methods of ShaderModuleGLSLDescriptor
void ShaderModuleGLSLDescriptor::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	((StringView*)&code)->setDefault();
	chain.sType = (WGPUSType)NativeSType::ShaderModuleGLSLDescriptor;
}


// Methods of ShaderModuleDescriptorSpirV
void ShaderModuleDescriptorSpirV::setDefault() {
	((StringView*)&label)->setDefault();
}


// Methods of RegistryReport
void RegistryReport::setDefault() {
}


// Methods of HubReport
void HubReport::setDefault() {
	((RegistryReport*)&adapters)->setDefault();
	((RegistryReport*)&devices)->setDefault();
	((RegistryReport*)&queues)->setDefault();
	((RegistryReport*)&pipelineLayouts)->setDefault();
	((RegistryReport*)&shaderModules)->setDefault();
	((RegistryReport*)&bindGroupLayouts)->setDefault();
	((RegistryReport*)&bindGroups)->setDefault();
	((RegistryReport*)&commandBuffers)->setDefault();
	((RegistryReport*)&renderBundles)->setDefault();
	((RegistryReport*)&renderPipelines)->setDefault();
	((RegistryReport*)&computePipelines)->setDefault();
	((RegistryReport*)&pipelineCaches)->setDefault();
	((RegistryReport*)&querySets)->setDefault();
	((RegistryReport*)&buffers)->setDefault();
	((RegistryReport*)&textures)->setDefault();
	((RegistryReport*)&textureViews)->setDefault();
	((RegistryReport*)&samplers)->setDefault();
}


// Methods of GlobalReport
void GlobalReport::setDefault() {
	((RegistryReport*)&surfaces)->setDefault();
	((HubReport*)&hub)->setDefault();
}


// Methods of InstanceEnumerateAdapterOptions
void InstanceEnumerateAdapterOptions::setDefault() {
}


// Methods of BindGroupEntryExtras
void BindGroupEntryExtras::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	chain.sType = (WGPUSType)NativeSType::BindGroupEntryExtras;
}


// Methods of BindGroupLayoutEntryExtras
void BindGroupLayoutEntryExtras::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	chain.sType = (WGPUSType)NativeSType::BindGroupLayoutEntryExtras;
}


// Methods of QuerySetDescriptorExtras
void QuerySetDescriptorExtras::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	chain.sType = (WGPUSType)NativeSType::QuerySetDescriptorExtras;
}


// Methods of SurfaceConfigurationExtras
void SurfaceConfigurationExtras::setDefault() {
	((ChainedStruct*)&chain)->setDefault();
	chain.sType = (WGPUSType)NativeSType::SurfaceConfigurationExtras;
}


// Methods of Adapter
void Adapter::getFeatures(SupportedFeatures * features) {
	return wgpuAdapterGetFeatures(m_raw, features);
}
Status Adapter::getInfo(AdapterInfo * info) {
	return static_cast<Status>(wgpuAdapterGetInfo(m_raw, info));
}
Status Adapter::getLimits(Limits * limits) {
	return static_cast<Status>(wgpuAdapterGetLimits(m_raw, limits));
}
Bool Adapter::hasFeature(FeatureName feature) {
	return wgpuAdapterHasFeature(m_raw, static_cast<WGPUFeatureName>(feature));
}
Future Adapter::requestDevice(const DeviceDescriptor& descriptor, RequestDeviceCallbackInfo callbackInfo) {
	return wgpuAdapterRequestDevice(m_raw, &descriptor, callbackInfo);
}
void Adapter::addRef() {
	return wgpuAdapterAddRef(m_raw);
}
void Adapter::release() {
	return wgpuAdapterRelease(m_raw);
}


// Methods of BindGroup
void BindGroup::setLabel(StringView label) {
	return wgpuBindGroupSetLabel(m_raw, label);
}
void BindGroup::addRef() {
	return wgpuBindGroupAddRef(m_raw);
}
void BindGroup::release() {
	return wgpuBindGroupRelease(m_raw);
}


// Methods of BindGroupLayout
void BindGroupLayout::setLabel(StringView label) {
	return wgpuBindGroupLayoutSetLabel(m_raw, label);
}
void BindGroupLayout::addRef() {
	return wgpuBindGroupLayoutAddRef(m_raw);
}
void BindGroupLayout::release() {
	return wgpuBindGroupLayoutRelease(m_raw);
}


// Methods of Buffer
void Buffer::destroy() {
	return wgpuBufferDestroy(m_raw);
}
void const * Buffer::getConstMappedRange(size_t offset, size_t size) {
	return wgpuBufferGetConstMappedRange(m_raw, offset, size);
}
BufferMapState Buffer::getMapState() {
	return static_cast<BufferMapState>(wgpuBufferGetMapState(m_raw));
}
void * Buffer::getMappedRange(size_t offset, size_t size) {
	return wgpuBufferGetMappedRange(m_raw, offset, size);
}
uint64_t Buffer::getSize() {
	return wgpuBufferGetSize(m_raw);
}
BufferUsage Buffer::getUsage() {
	return static_cast<BufferUsage>(wgpuBufferGetUsage(m_raw));
}
Future Buffer::mapAsync(MapMode mode, size_t offset, size_t size, BufferMapCallbackInfo callbackInfo) {
	return wgpuBufferMapAsync(m_raw, static_cast<WGPUMapMode>(mode), offset, size, callbackInfo);
}
void Buffer::setLabel(StringView label) {
	return wgpuBufferSetLabel(m_raw, label);
}
void Buffer::unmap() {
	return wgpuBufferUnmap(m_raw);
}
void Buffer::addRef() {
	return wgpuBufferAddRef(m_raw);
}
void Buffer::release() {
	return wgpuBufferRelease(m_raw);
}


// Methods of CommandBuffer
void CommandBuffer::setLabel(StringView label) {
	return wgpuCommandBufferSetLabel(m_raw, label);
}
void CommandBuffer::addRef() {
	return wgpuCommandBufferAddRef(m_raw);
}
void CommandBuffer::release() {
	return wgpuCommandBufferRelease(m_raw);
}


// Methods of CommandEncoder
ComputePassEncoder CommandEncoder::beginComputePass(const ComputePassDescriptor& descriptor) {
	return wgpuCommandEncoderBeginComputePass(m_raw, &descriptor);
}
ComputePassEncoder CommandEncoder::beginComputePass() {
	return wgpuCommandEncoderBeginComputePass(m_raw, nullptr);
}
RenderPassEncoder CommandEncoder::beginRenderPass(const RenderPassDescriptor& descriptor) {
	return wgpuCommandEncoderBeginRenderPass(m_raw, &descriptor);
}
void CommandEncoder::clearBuffer(Buffer buffer, uint64_t offset, uint64_t size) {
	return wgpuCommandEncoderClearBuffer(m_raw, buffer, offset, size);
}
void CommandEncoder::copyBufferToBuffer(Buffer source, uint64_t sourceOffset, Buffer destination, uint64_t destinationOffset, uint64_t size) {
	return wgpuCommandEncoderCopyBufferToBuffer(m_raw, source, sourceOffset, destination, destinationOffset, size);
}
void CommandEncoder::copyBufferToTexture(const TexelCopyBufferInfo& source, const TexelCopyTextureInfo& destination, const Extent3D& copySize) {
	return wgpuCommandEncoderCopyBufferToTexture(m_raw, &source, &destination, &copySize);
}
void CommandEncoder::copyTextureToBuffer(const TexelCopyTextureInfo& source, const TexelCopyBufferInfo& destination, const Extent3D& copySize) {
	return wgpuCommandEncoderCopyTextureToBuffer(m_raw, &source, &destination, &copySize);
}
void CommandEncoder::copyTextureToTexture(const TexelCopyTextureInfo& source, const TexelCopyTextureInfo& destination, const Extent3D& copySize) {
	return wgpuCommandEncoderCopyTextureToTexture(m_raw, &source, &destination, &copySize);
}
CommandBuffer CommandEncoder::finish(const CommandBufferDescriptor& descriptor) {
	return wgpuCommandEncoderFinish(m_raw, &descriptor);
}
CommandBuffer CommandEncoder::finish() {
	return wgpuCommandEncoderFinish(m_raw, nullptr);
}
void CommandEncoder::insertDebugMarker(StringView markerLabel) {
	return wgpuCommandEncoderInsertDebugMarker(m_raw, markerLabel);
}
void CommandEncoder::popDebugGroup() {
	return wgpuCommandEncoderPopDebugGroup(m_raw);
}
void CommandEncoder::pushDebugGroup(StringView groupLabel) {
	return wgpuCommandEncoderPushDebugGroup(m_raw, groupLabel);
}
void CommandEncoder::resolveQuerySet(QuerySet querySet, uint32_t firstQuery, uint32_t queryCount, Buffer destination, uint64_t destinationOffset) {
	return wgpuCommandEncoderResolveQuerySet(m_raw, querySet, firstQuery, queryCount, destination, destinationOffset);
}
void CommandEncoder::setLabel(StringView label) {
	return wgpuCommandEncoderSetLabel(m_raw, label);
}
void CommandEncoder::writeTimestamp(QuerySet querySet, uint32_t queryIndex) {
	return wgpuCommandEncoderWriteTimestamp(m_raw, querySet, queryIndex);
}
void CommandEncoder::addRef() {
	return wgpuCommandEncoderAddRef(m_raw);
}
void CommandEncoder::release() {
	return wgpuCommandEncoderRelease(m_raw);
}


// Methods of ComputePassEncoder
void ComputePassEncoder::dispatchWorkgroups(uint32_t workgroupCountX, uint32_t workgroupCountY, uint32_t workgroupCountZ) {
	return wgpuComputePassEncoderDispatchWorkgroups(m_raw, workgroupCountX, workgroupCountY, workgroupCountZ);
}
void ComputePassEncoder::dispatchWorkgroupsIndirect(Buffer indirectBuffer, uint64_t indirectOffset) {
	return wgpuComputePassEncoderDispatchWorkgroupsIndirect(m_raw, indirectBuffer, indirectOffset);
}
void ComputePassEncoder::end() {
	return wgpuComputePassEncoderEnd(m_raw);
}
void ComputePassEncoder::insertDebugMarker(StringView markerLabel) {
	return wgpuComputePassEncoderInsertDebugMarker(m_raw, markerLabel);
}
void ComputePassEncoder::popDebugGroup() {
	return wgpuComputePassEncoderPopDebugGroup(m_raw);
}
void ComputePassEncoder::pushDebugGroup(StringView groupLabel) {
	return wgpuComputePassEncoderPushDebugGroup(m_raw, groupLabel);
}
void ComputePassEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, size_t dynamicOffsetCount, uint32_t const * dynamicOffsets) {
	return wgpuComputePassEncoderSetBindGroup(m_raw, groupIndex, group, dynamicOffsetCount, dynamicOffsets);
}
void ComputePassEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, const std::vector<uint32_t>& dynamicOffsets) {
	return wgpuComputePassEncoderSetBindGroup(m_raw, groupIndex, group, static_cast<size_t>(dynamicOffsets.size()), dynamicOffsets.data());
}
void ComputePassEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, const uint32_t& dynamicOffsets) {
	return wgpuComputePassEncoderSetBindGroup(m_raw, groupIndex, group, 1, &dynamicOffsets);
}
void ComputePassEncoder::setLabel(StringView label) {
	return wgpuComputePassEncoderSetLabel(m_raw, label);
}
void ComputePassEncoder::setPipeline(ComputePipeline pipeline) {
	return wgpuComputePassEncoderSetPipeline(m_raw, pipeline);
}
void ComputePassEncoder::addRef() {
	return wgpuComputePassEncoderAddRef(m_raw);
}
void ComputePassEncoder::release() {
	return wgpuComputePassEncoderRelease(m_raw);
}
void ComputePassEncoder::setPushConstants(uint32_t offset, uint32_t sizeBytes, void const * data) {
	return wgpuComputePassEncoderSetPushConstants(m_raw, offset, sizeBytes, data);
}
void ComputePassEncoder::beginPipelineStatisticsQuery(QuerySet querySet, uint32_t queryIndex) {
	return wgpuComputePassEncoderBeginPipelineStatisticsQuery(m_raw, querySet, queryIndex);
}
void ComputePassEncoder::endPipelineStatisticsQuery() {
	return wgpuComputePassEncoderEndPipelineStatisticsQuery(m_raw);
}
void ComputePassEncoder::writeTimestamp(QuerySet querySet, uint32_t queryIndex) {
	return wgpuComputePassEncoderWriteTimestamp(m_raw, querySet, queryIndex);
}


// Methods of ComputePipeline
BindGroupLayout ComputePipeline::getBindGroupLayout(uint32_t groupIndex) {
	return wgpuComputePipelineGetBindGroupLayout(m_raw, groupIndex);
}
void ComputePipeline::setLabel(StringView label) {
	return wgpuComputePipelineSetLabel(m_raw, label);
}
void ComputePipeline::addRef() {
	return wgpuComputePipelineAddRef(m_raw);
}
void ComputePipeline::release() {
	return wgpuComputePipelineRelease(m_raw);
}


// Methods of Device
BindGroup Device::createBindGroup(const BindGroupDescriptor& descriptor) {
	return wgpuDeviceCreateBindGroup(m_raw, &descriptor);
}
BindGroupLayout Device::createBindGroupLayout(const BindGroupLayoutDescriptor& descriptor) {
	return wgpuDeviceCreateBindGroupLayout(m_raw, &descriptor);
}
Buffer Device::createBuffer(const BufferDescriptor& descriptor) {
	return wgpuDeviceCreateBuffer(m_raw, &descriptor);
}
CommandEncoder Device::createCommandEncoder(const CommandEncoderDescriptor& descriptor) {
	return wgpuDeviceCreateCommandEncoder(m_raw, &descriptor);
}
CommandEncoder Device::createCommandEncoder() {
	return wgpuDeviceCreateCommandEncoder(m_raw, nullptr);
}
ComputePipeline Device::createComputePipeline(const ComputePipelineDescriptor& descriptor) {
	return wgpuDeviceCreateComputePipeline(m_raw, &descriptor);
}
Future Device::createComputePipelineAsync(const ComputePipelineDescriptor& descriptor, CreateComputePipelineAsyncCallbackInfo callbackInfo) {
	return wgpuDeviceCreateComputePipelineAsync(m_raw, &descriptor, callbackInfo);
}
PipelineLayout Device::createPipelineLayout(const PipelineLayoutDescriptor& descriptor) {
	return wgpuDeviceCreatePipelineLayout(m_raw, &descriptor);
}
QuerySet Device::createQuerySet(const QuerySetDescriptor& descriptor) {
	return wgpuDeviceCreateQuerySet(m_raw, &descriptor);
}
RenderBundleEncoder Device::createRenderBundleEncoder(const RenderBundleEncoderDescriptor& descriptor) {
	return wgpuDeviceCreateRenderBundleEncoder(m_raw, &descriptor);
}
RenderPipeline Device::createRenderPipeline(const RenderPipelineDescriptor& descriptor) {
	return wgpuDeviceCreateRenderPipeline(m_raw, &descriptor);
}
Future Device::createRenderPipelineAsync(const RenderPipelineDescriptor& descriptor, CreateRenderPipelineAsyncCallbackInfo callbackInfo) {
	return wgpuDeviceCreateRenderPipelineAsync(m_raw, &descriptor, callbackInfo);
}
Sampler Device::createSampler(const SamplerDescriptor& descriptor) {
	return wgpuDeviceCreateSampler(m_raw, &descriptor);
}
Sampler Device::createSampler() {
	return wgpuDeviceCreateSampler(m_raw, nullptr);
}
ShaderModule Device::createShaderModule(const ShaderModuleDescriptor& descriptor) {
	return wgpuDeviceCreateShaderModule(m_raw, &descriptor);
}
Texture Device::createTexture(const TextureDescriptor& descriptor) {
	return wgpuDeviceCreateTexture(m_raw, &descriptor);
}
void Device::destroy() {
	return wgpuDeviceDestroy(m_raw);
}
AdapterInfo Device::getAdapterInfo() {
	return wgpuDeviceGetAdapterInfo(m_raw);
}
void Device::getFeatures(SupportedFeatures * features) {
	return wgpuDeviceGetFeatures(m_raw, features);
}
Status Device::getLimits(Limits * limits) {
	return static_cast<Status>(wgpuDeviceGetLimits(m_raw, limits));
}
Future Device::getLostFuture() {
	return wgpuDeviceGetLostFuture(m_raw);
}
Queue Device::getQueue() {
	return wgpuDeviceGetQueue(m_raw);
}
Bool Device::hasFeature(FeatureName feature) {
	return wgpuDeviceHasFeature(m_raw, static_cast<WGPUFeatureName>(feature));
}
Future Device::popErrorScope(PopErrorScopeCallbackInfo callbackInfo) {
	return wgpuDevicePopErrorScope(m_raw, callbackInfo);
}
void Device::pushErrorScope(ErrorFilter filter) {
	return wgpuDevicePushErrorScope(m_raw, static_cast<WGPUErrorFilter>(filter));
}
void Device::setLabel(StringView label) {
	return wgpuDeviceSetLabel(m_raw, label);
}
void Device::addRef() {
	return wgpuDeviceAddRef(m_raw);
}
void Device::release() {
	return wgpuDeviceRelease(m_raw);
}
Bool Device::poll(Bool wait, SubmissionIndex const * wrappedSubmissionIndex) {
	return wgpuDevicePoll(m_raw, wait, wrappedSubmissionIndex);
}
ShaderModule Device::createShaderModuleSpirV(const ShaderModuleDescriptorSpirV& descriptor) {
	return wgpuDeviceCreateShaderModuleSpirV(m_raw, &descriptor);
}


// Methods of Instance
Surface Instance::createSurface(const SurfaceDescriptor& descriptor) {
	return wgpuInstanceCreateSurface(m_raw, &descriptor);
}
Status Instance::getWGSLLanguageFeatures(SupportedWGSLLanguageFeatures * features) {
	return static_cast<Status>(wgpuInstanceGetWGSLLanguageFeatures(m_raw, features));
}
Bool Instance::hasWGSLLanguageFeature(WGSLLanguageFeatureName feature) {
	return wgpuInstanceHasWGSLLanguageFeature(m_raw, static_cast<WGPUWGSLLanguageFeatureName>(feature));
}
void Instance::processEvents() {
	return wgpuInstanceProcessEvents(m_raw);
}
Future Instance::requestAdapter(const RequestAdapterOptions& options, RequestAdapterCallbackInfo callbackInfo) {
	return wgpuInstanceRequestAdapter(m_raw, &options, callbackInfo);
}
WaitStatus Instance::waitAny(size_t futureCount, FutureWaitInfo * futures, uint64_t timeoutNS) {
	return static_cast<WaitStatus>(wgpuInstanceWaitAny(m_raw, futureCount, futures, timeoutNS));
}
void Instance::addRef() {
	return wgpuInstanceAddRef(m_raw);
}
void Instance::release() {
	return wgpuInstanceRelease(m_raw);
}
size_t Instance::enumerateAdapters(const InstanceEnumerateAdapterOptions& options, Adapter * adapters) {
	return wgpuInstanceEnumerateAdapters(m_raw, &options, reinterpret_cast<WGPUAdapter *>(adapters));
}


// Methods of PipelineLayout
void PipelineLayout::setLabel(StringView label) {
	return wgpuPipelineLayoutSetLabel(m_raw, label);
}
void PipelineLayout::addRef() {
	return wgpuPipelineLayoutAddRef(m_raw);
}
void PipelineLayout::release() {
	return wgpuPipelineLayoutRelease(m_raw);
}


// Methods of QuerySet
void QuerySet::destroy() {
	return wgpuQuerySetDestroy(m_raw);
}
uint32_t QuerySet::getCount() {
	return wgpuQuerySetGetCount(m_raw);
}
QueryType QuerySet::getType() {
	return static_cast<QueryType>(wgpuQuerySetGetType(m_raw));
}
void QuerySet::setLabel(StringView label) {
	return wgpuQuerySetSetLabel(m_raw, label);
}
void QuerySet::addRef() {
	return wgpuQuerySetAddRef(m_raw);
}
void QuerySet::release() {
	return wgpuQuerySetRelease(m_raw);
}


// Methods of Queue
Future Queue::onSubmittedWorkDone(QueueWorkDoneCallbackInfo callbackInfo) {
	return wgpuQueueOnSubmittedWorkDone(m_raw, callbackInfo);
}
void Queue::setLabel(StringView label) {
	return wgpuQueueSetLabel(m_raw, label);
}
void Queue::submit(size_t commandCount, CommandBuffer const * commands) {
	return wgpuQueueSubmit(m_raw, commandCount, reinterpret_cast<WGPUCommandBuffer const *>(commands));
}
void Queue::submit(const std::vector<WGPUCommandBuffer>& commands) {
	return wgpuQueueSubmit(m_raw, static_cast<size_t>(commands.size()), commands.data());
}
void Queue::submit(const WGPUCommandBuffer& commands) {
	return wgpuQueueSubmit(m_raw, 1, &commands);
}
void Queue::writeBuffer(Buffer buffer, uint64_t bufferOffset, void const * data, size_t size) {
	return wgpuQueueWriteBuffer(m_raw, buffer, bufferOffset, data, size);
}
void Queue::writeTexture(const TexelCopyTextureInfo& destination, void const * data, size_t dataSize, const TexelCopyBufferLayout& dataLayout, const Extent3D& writeSize) {
	return wgpuQueueWriteTexture(m_raw, &destination, data, dataSize, &dataLayout, &writeSize);
}
void Queue::addRef() {
	return wgpuQueueAddRef(m_raw);
}
void Queue::release() {
	return wgpuQueueRelease(m_raw);
}
SubmissionIndex Queue::submitForIndex(size_t commandCount, CommandBuffer const * commands) {
	return wgpuQueueSubmitForIndex(m_raw, commandCount, reinterpret_cast<WGPUCommandBuffer const *>(commands));
}
SubmissionIndex Queue::submitForIndex(const std::vector<WGPUCommandBuffer>& commands) {
	return wgpuQueueSubmitForIndex(m_raw, static_cast<size_t>(commands.size()), commands.data());
}
SubmissionIndex Queue::submitForIndex(const WGPUCommandBuffer& commands) {
	return wgpuQueueSubmitForIndex(m_raw, 1, &commands);
}


// Methods of RenderBundle
void RenderBundle::setLabel(StringView label) {
	return wgpuRenderBundleSetLabel(m_raw, label);
}
void RenderBundle::addRef() {
	return wgpuRenderBundleAddRef(m_raw);
}
void RenderBundle::release() {
	return wgpuRenderBundleRelease(m_raw);
}


// Methods of RenderBundleEncoder
void RenderBundleEncoder::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance) {
	return wgpuRenderBundleEncoderDraw(m_raw, vertexCount, instanceCount, firstVertex, firstInstance);
}
void RenderBundleEncoder::drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t baseVertex, uint32_t firstInstance) {
	return wgpuRenderBundleEncoderDrawIndexed(m_raw, indexCount, instanceCount, firstIndex, baseVertex, firstInstance);
}
void RenderBundleEncoder::drawIndexedIndirect(Buffer indirectBuffer, uint64_t indirectOffset) {
	return wgpuRenderBundleEncoderDrawIndexedIndirect(m_raw, indirectBuffer, indirectOffset);
}
void RenderBundleEncoder::drawIndirect(Buffer indirectBuffer, uint64_t indirectOffset) {
	return wgpuRenderBundleEncoderDrawIndirect(m_raw, indirectBuffer, indirectOffset);
}
RenderBundle RenderBundleEncoder::finish(const RenderBundleDescriptor& descriptor) {
	return wgpuRenderBundleEncoderFinish(m_raw, &descriptor);
}
RenderBundle RenderBundleEncoder::finish() {
	return wgpuRenderBundleEncoderFinish(m_raw, nullptr);
}
void RenderBundleEncoder::insertDebugMarker(StringView markerLabel) {
	return wgpuRenderBundleEncoderInsertDebugMarker(m_raw, markerLabel);
}
void RenderBundleEncoder::popDebugGroup() {
	return wgpuRenderBundleEncoderPopDebugGroup(m_raw);
}
void RenderBundleEncoder::pushDebugGroup(StringView groupLabel) {
	return wgpuRenderBundleEncoderPushDebugGroup(m_raw, groupLabel);
}
void RenderBundleEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, size_t dynamicOffsetCount, uint32_t const * dynamicOffsets) {
	return wgpuRenderBundleEncoderSetBindGroup(m_raw, groupIndex, group, dynamicOffsetCount, dynamicOffsets);
}
void RenderBundleEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, const std::vector<uint32_t>& dynamicOffsets) {
	return wgpuRenderBundleEncoderSetBindGroup(m_raw, groupIndex, group, static_cast<size_t>(dynamicOffsets.size()), dynamicOffsets.data());
}
void RenderBundleEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, const uint32_t& dynamicOffsets) {
	return wgpuRenderBundleEncoderSetBindGroup(m_raw, groupIndex, group, 1, &dynamicOffsets);
}
void RenderBundleEncoder::setIndexBuffer(Buffer buffer, IndexFormat format, uint64_t offset, uint64_t size) {
	return wgpuRenderBundleEncoderSetIndexBuffer(m_raw, buffer, static_cast<WGPUIndexFormat>(format), offset, size);
}
void RenderBundleEncoder::setLabel(StringView label) {
	return wgpuRenderBundleEncoderSetLabel(m_raw, label);
}
void RenderBundleEncoder::setPipeline(RenderPipeline pipeline) {
	return wgpuRenderBundleEncoderSetPipeline(m_raw, pipeline);
}
void RenderBundleEncoder::setVertexBuffer(uint32_t slot, Buffer buffer, uint64_t offset, uint64_t size) {
	return wgpuRenderBundleEncoderSetVertexBuffer(m_raw, slot, buffer, offset, size);
}
void RenderBundleEncoder::addRef() {
	return wgpuRenderBundleEncoderAddRef(m_raw);
}
void RenderBundleEncoder::release() {
	return wgpuRenderBundleEncoderRelease(m_raw);
}


// Methods of RenderPassEncoder
void RenderPassEncoder::beginOcclusionQuery(uint32_t queryIndex) {
	return wgpuRenderPassEncoderBeginOcclusionQuery(m_raw, queryIndex);
}
void RenderPassEncoder::draw(uint32_t vertexCount, uint32_t instanceCount, uint32_t firstVertex, uint32_t firstInstance) {
	return wgpuRenderPassEncoderDraw(m_raw, vertexCount, instanceCount, firstVertex, firstInstance);
}
void RenderPassEncoder::drawIndexed(uint32_t indexCount, uint32_t instanceCount, uint32_t firstIndex, int32_t baseVertex, uint32_t firstInstance) {
	return wgpuRenderPassEncoderDrawIndexed(m_raw, indexCount, instanceCount, firstIndex, baseVertex, firstInstance);
}
void RenderPassEncoder::drawIndexedIndirect(Buffer indirectBuffer, uint64_t indirectOffset) {
	return wgpuRenderPassEncoderDrawIndexedIndirect(m_raw, indirectBuffer, indirectOffset);
}
void RenderPassEncoder::drawIndirect(Buffer indirectBuffer, uint64_t indirectOffset) {
	return wgpuRenderPassEncoderDrawIndirect(m_raw, indirectBuffer, indirectOffset);
}
void RenderPassEncoder::end() {
	return wgpuRenderPassEncoderEnd(m_raw);
}
void RenderPassEncoder::endOcclusionQuery() {
	return wgpuRenderPassEncoderEndOcclusionQuery(m_raw);
}
void RenderPassEncoder::executeBundles(size_t bundleCount, RenderBundle const * bundles) {
	return wgpuRenderPassEncoderExecuteBundles(m_raw, bundleCount, reinterpret_cast<WGPURenderBundle const *>(bundles));
}
void RenderPassEncoder::executeBundles(const std::vector<WGPURenderBundle>& bundles) {
	return wgpuRenderPassEncoderExecuteBundles(m_raw, static_cast<size_t>(bundles.size()), bundles.data());
}
void RenderPassEncoder::executeBundles(const WGPURenderBundle& bundles) {
	return wgpuRenderPassEncoderExecuteBundles(m_raw, 1, &bundles);
}
void RenderPassEncoder::insertDebugMarker(StringView markerLabel) {
	return wgpuRenderPassEncoderInsertDebugMarker(m_raw, markerLabel);
}
void RenderPassEncoder::popDebugGroup() {
	return wgpuRenderPassEncoderPopDebugGroup(m_raw);
}
void RenderPassEncoder::pushDebugGroup(StringView groupLabel) {
	return wgpuRenderPassEncoderPushDebugGroup(m_raw, groupLabel);
}
void RenderPassEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, size_t dynamicOffsetCount, uint32_t const * dynamicOffsets) {
	return wgpuRenderPassEncoderSetBindGroup(m_raw, groupIndex, group, dynamicOffsetCount, dynamicOffsets);
}
void RenderPassEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, const std::vector<uint32_t>& dynamicOffsets) {
	return wgpuRenderPassEncoderSetBindGroup(m_raw, groupIndex, group, static_cast<size_t>(dynamicOffsets.size()), dynamicOffsets.data());
}
void RenderPassEncoder::setBindGroup(uint32_t groupIndex, BindGroup group, const uint32_t& dynamicOffsets) {
	return wgpuRenderPassEncoderSetBindGroup(m_raw, groupIndex, group, 1, &dynamicOffsets);
}
void RenderPassEncoder::setBlendConstant(const Color& color) {
	return wgpuRenderPassEncoderSetBlendConstant(m_raw, &color);
}
void RenderPassEncoder::setIndexBuffer(Buffer buffer, IndexFormat format, uint64_t offset, uint64_t size) {
	return wgpuRenderPassEncoderSetIndexBuffer(m_raw, buffer, static_cast<WGPUIndexFormat>(format), offset, size);
}
void RenderPassEncoder::setLabel(StringView label) {
	return wgpuRenderPassEncoderSetLabel(m_raw, label);
}
void RenderPassEncoder::setPipeline(RenderPipeline pipeline) {
	return wgpuRenderPassEncoderSetPipeline(m_raw, pipeline);
}
void RenderPassEncoder::setScissorRect(uint32_t x, uint32_t y, uint32_t width, uint32_t height) {
	return wgpuRenderPassEncoderSetScissorRect(m_raw, x, y, width, height);
}
void RenderPassEncoder::setStencilReference(uint32_t reference) {
	return wgpuRenderPassEncoderSetStencilReference(m_raw, reference);
}
void RenderPassEncoder::setVertexBuffer(uint32_t slot, Buffer buffer, uint64_t offset, uint64_t size) {
	return wgpuRenderPassEncoderSetVertexBuffer(m_raw, slot, buffer, offset, size);
}
void RenderPassEncoder::setViewport(float x, float y, float width, float height, float minDepth, float maxDepth) {
	return wgpuRenderPassEncoderSetViewport(m_raw, x, y, width, height, minDepth, maxDepth);
}
void RenderPassEncoder::addRef() {
	return wgpuRenderPassEncoderAddRef(m_raw);
}
void RenderPassEncoder::release() {
	return wgpuRenderPassEncoderRelease(m_raw);
}
void RenderPassEncoder::setPushConstants(ShaderStage stages, uint32_t offset, uint32_t sizeBytes, void const * data) {
	return wgpuRenderPassEncoderSetPushConstants(m_raw, static_cast<WGPUShaderStage>(stages), offset, sizeBytes, data);
}
void RenderPassEncoder::multiDrawIndirect(Buffer buffer, uint64_t offset, uint32_t count) {
	return wgpuRenderPassEncoderMultiDrawIndirect(m_raw, buffer, offset, count);
}
void RenderPassEncoder::multiDrawIndexedIndirect(Buffer buffer, uint64_t offset, uint32_t count) {
	return wgpuRenderPassEncoderMultiDrawIndexedIndirect(m_raw, buffer, offset, count);
}
void RenderPassEncoder::multiDrawIndirectCount(Buffer buffer, uint64_t offset, Buffer count_buffer, uint64_t count_buffer_offset, uint32_t max_count) {
	return wgpuRenderPassEncoderMultiDrawIndirectCount(m_raw, buffer, offset, count_buffer, count_buffer_offset, max_count);
}
void RenderPassEncoder::multiDrawIndexedIndirectCount(Buffer buffer, uint64_t offset, Buffer count_buffer, uint64_t count_buffer_offset, uint32_t max_count) {
	return wgpuRenderPassEncoderMultiDrawIndexedIndirectCount(m_raw, buffer, offset, count_buffer, count_buffer_offset, max_count);
}
void RenderPassEncoder::beginPipelineStatisticsQuery(QuerySet querySet, uint32_t queryIndex) {
	return wgpuRenderPassEncoderBeginPipelineStatisticsQuery(m_raw, querySet, queryIndex);
}
void RenderPassEncoder::endPipelineStatisticsQuery() {
	return wgpuRenderPassEncoderEndPipelineStatisticsQuery(m_raw);
}
void RenderPassEncoder::writeTimestamp(QuerySet querySet, uint32_t queryIndex) {
	return wgpuRenderPassEncoderWriteTimestamp(m_raw, querySet, queryIndex);
}


// Methods of RenderPipeline
BindGroupLayout RenderPipeline::getBindGroupLayout(uint32_t groupIndex) {
	return wgpuRenderPipelineGetBindGroupLayout(m_raw, groupIndex);
}
void RenderPipeline::setLabel(StringView label) {
	return wgpuRenderPipelineSetLabel(m_raw, label);
}
void RenderPipeline::addRef() {
	return wgpuRenderPipelineAddRef(m_raw);
}
void RenderPipeline::release() {
	return wgpuRenderPipelineRelease(m_raw);
}


// Methods of Sampler
void Sampler::setLabel(StringView label) {
	return wgpuSamplerSetLabel(m_raw, label);
}
void Sampler::addRef() {
	return wgpuSamplerAddRef(m_raw);
}
void Sampler::release() {
	return wgpuSamplerRelease(m_raw);
}


// Methods of ShaderModule
Future ShaderModule::getCompilationInfo(CompilationInfoCallbackInfo callbackInfo) {
	return wgpuShaderModuleGetCompilationInfo(m_raw, callbackInfo);
}
void ShaderModule::setLabel(StringView label) {
	return wgpuShaderModuleSetLabel(m_raw, label);
}
void ShaderModule::addRef() {
	return wgpuShaderModuleAddRef(m_raw);
}
void ShaderModule::release() {
	return wgpuShaderModuleRelease(m_raw);
}


// Methods of Surface
void Surface::configure(const SurfaceConfiguration& config) {
	return wgpuSurfaceConfigure(m_raw, &config);
}
Status Surface::getCapabilities(Adapter adapter, SurfaceCapabilities * capabilities) {
	return static_cast<Status>(wgpuSurfaceGetCapabilities(m_raw, adapter, capabilities));
}
void Surface::getCurrentTexture(SurfaceTexture * surfaceTexture) {
	return wgpuSurfaceGetCurrentTexture(m_raw, surfaceTexture);
}
Status Surface::present() {
	return static_cast<Status>(wgpuSurfacePresent(m_raw));
}
void Surface::setLabel(StringView label) {
	return wgpuSurfaceSetLabel(m_raw, label);
}
void Surface::unconfigure() {
	return wgpuSurfaceUnconfigure(m_raw);
}
void Surface::addRef() {
	return wgpuSurfaceAddRef(m_raw);
}
void Surface::release() {
	return wgpuSurfaceRelease(m_raw);
}


// Methods of Texture
TextureView Texture::createView(const TextureViewDescriptor& descriptor) {
	return wgpuTextureCreateView(m_raw, &descriptor);
}
TextureView Texture::createView() {
	return wgpuTextureCreateView(m_raw, nullptr);
}
void Texture::destroy() {
	return wgpuTextureDestroy(m_raw);
}
uint32_t Texture::getDepthOrArrayLayers() {
	return wgpuTextureGetDepthOrArrayLayers(m_raw);
}
TextureDimension Texture::getDimension() {
	return static_cast<TextureDimension>(wgpuTextureGetDimension(m_raw));
}
TextureFormat Texture::getFormat() {
	return static_cast<TextureFormat>(wgpuTextureGetFormat(m_raw));
}
uint32_t Texture::getHeight() {
	return wgpuTextureGetHeight(m_raw);
}
uint32_t Texture::getMipLevelCount() {
	return wgpuTextureGetMipLevelCount(m_raw);
}
uint32_t Texture::getSampleCount() {
	return wgpuTextureGetSampleCount(m_raw);
}
TextureUsage Texture::getUsage() {
	return static_cast<TextureUsage>(wgpuTextureGetUsage(m_raw));
}
uint32_t Texture::getWidth() {
	return wgpuTextureGetWidth(m_raw);
}
void Texture::setLabel(StringView label) {
	return wgpuTextureSetLabel(m_raw, label);
}
void Texture::addRef() {
	return wgpuTextureAddRef(m_raw);
}
void Texture::release() {
	return wgpuTextureRelease(m_raw);
}


// Methods of TextureView
void TextureView::setLabel(StringView label) {
	return wgpuTextureViewSetLabel(m_raw, label);
}
void TextureView::addRef() {
	return wgpuTextureViewAddRef(m_raw);
}
void TextureView::release() {
	return wgpuTextureViewRelease(m_raw);
}



// Extra implementations
Adapter Instance::requestAdapter(const RequestAdapterOptions& options) {
	struct Context {
		Adapter adapter = nullptr;
		bool requestEnded = false;
	};
	Context context;

	RequestAdapterCallbackInfo callbackInfo;
	callbackInfo.nextInChain = nullptr;
	callbackInfo.userdata1 = &context;
	callbackInfo.callback = [](
		WGPURequestAdapterStatus status,
		WGPUAdapter adapter,
		WGPUStringView message,
		void* userdata1,
		[[maybe_unused]] void* userdata2
	) {
		Context& context = *reinterpret_cast<Context*>(userdata1);
		if (status == RequestAdapterStatus::Success) {
			context.adapter = adapter;
		}
		else {
			std::cout << "Could not get WebGPU adapter: " << StringView(message) << std::endl;
		}
		context.requestEnded = true;
	};
	callbackInfo.mode = CallbackMode::AllowSpontaneous;
	requestAdapter(options, callbackInfo);

#if __EMSCRIPTEN__
	while (!context.requestEnded) {
		emscripten_sleep(50);
	}
#endif

	assert(context.requestEnded);
	return context.adapter;
}

Device Adapter::requestDevice(const DeviceDescriptor& descriptor) {
	struct Context {
		Device device = nullptr;
		bool requestEnded = false;
	};
	Context context;

	RequestDeviceCallbackInfo callbackInfo;
	callbackInfo.nextInChain = nullptr;
	callbackInfo.userdata1 = &context;
	callbackInfo.callback = [](
		WGPURequestDeviceStatus status,
		WGPUDevice device,
		WGPUStringView message,
		void* userdata1,
		[[maybe_unused]] void* userdata2
	) {
		Context& context = *reinterpret_cast<Context*>(userdata1);
		if (status == RequestDeviceStatus::Success) {
			context.device = device;
		}
		else {
			std::cout << "Could not get WebGPU device: " << StringView(message) << std::endl;
		}
		context.requestEnded = true;
	};
	callbackInfo.mode = CallbackMode::AllowSpontaneous;
	requestDevice(descriptor, callbackInfo);

#if __EMSCRIPTEN__
	while (!context.requestEnded) {
		emscripten_sleep(50);
	}
#endif

	assert(context.requestEnded);
	return context.device;
}

StringView::operator std::string_view() const {
	return
		length == WGPU_STRLEN
		? std::string_view(data)
		: std::string_view(data, length);
}

#endif // WEBGPU_CPP_IMPLEMENTATION

#undef HANDLE
#undef DESCRIPTOR
#undef ENUM
#undef ENUM_ENTRY
#undef END

} // namespace wgpu
