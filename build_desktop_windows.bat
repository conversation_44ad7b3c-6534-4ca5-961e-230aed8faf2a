@echo off
echo Building Desktop version for Windows...

REM Create build directory
if not exist "build_desktop" mkdir build_desktop
cd build_desktop

REM Configure with different WebGPU backends
echo.
echo ========================================
echo Choose WebGPU Backend:
echo 1. WGPU (Rust-based, default)
echo 2. DAWN (Chrome-based)
echo ========================================
set /p choice="Enter your choice (1-2): "

if "%choice%"=="2" (
    set BACKEND=DAWN
    echo Using Dawn backend...
) else (
    set BACKEND=WGPU
    echo Using wgpu-native backend...
)

REM Configure build type
echo.
echo ========================================
echo Choose Build Type:
echo 1. Release (default)
echo 2. Debug
echo ========================================
set /p build_choice="Enter your choice (1-2): "

if "%build_choice%"=="2" (
    set BUILD_TYPE=Debug
    echo Using Debug build...
) else (
    set BUILD_TYPE=Release
    echo Using Release build...
)

REM Configure CMake
echo.
echo Configuring CMake...
cmake .. -DCMAKE_BUILD_TYPE=%BUILD_TYPE% -DWEBGPU_BACKEND=%BACKEND% -DWEBGPU_BUILD_FROM_SOURCE=OFF

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    cd ..
    pause
    exit /b 1
)

REM Build the project
echo.
echo Building project...
cmake --build . --config %BUILD_TYPE% -j4

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    cd ..
    pause
    exit /b 1
)

REM Create redist directory if it doesn't exist
if not exist "..\redist_desktop" mkdir "..\redist_desktop"

REM Copy built files to redist directory
echo.
echo Copying files to redist_desktop...
if "%BUILD_TYPE%"=="Debug" (
    copy "Debug\App.exe" "..\redist_desktop\" 2>nul
    copy "Debug\*.dll" "..\redist_desktop\" 2>nul
) else (
    copy "Release\App.exe" "..\redist_desktop\" 2>nul
    copy "Release\*.dll" "..\redist_desktop\" 2>nul
)

REM Copy any additional DLLs from _deps
for /r "_deps" %%f in (*.dll) do (
    copy "%%f" "..\redist_desktop\" 2>nul
)

REM Copy resources
xcopy "..\resources" "..\redist_desktop\resources\" /E /I /Y 2>nul

cd ..

echo.
echo ========================================
echo Desktop build completed successfully!
echo Backend: %BACKEND%
echo Build Type: %BUILD_TYPE%
echo Files are in the redist_desktop directory.
echo Run redist_desktop\App.exe to test.
echo ========================================
