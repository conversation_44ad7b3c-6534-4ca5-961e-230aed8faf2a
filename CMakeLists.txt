cmake_minimum_required(VERSION 3.14...3.25)
project(
	LearnWebGPU
	VERSION 0.1.0
	LANGUAGES CXX C
)

include(FetchContent)
include(utils.cmake)

# We add an option to enable different settings when developping the app than
# when distributing it.
option(DEV_MODE "Set up development helper settings" ON)

# WebGPU backend selection
if(EMSCRIPTEN)
	set(WEBGPU_BACKEND_DEFAULT "EMDAWNWEBGPU")
else()
	set(WEBGPU_BACKEND_DEFAULT "WGPU")
endif()

set(WEBGPU_BACKEND ${WEBGPU_BACKEND_DEFAULT} CACHE STRING
	"Backend implementation of WebGPU. Possible values are EMSCRIPTE<PERSON>, EMDAWNWEBGPU, WGPU and DAWN")
set_property(CACHE WEBGPU_BACKEND PROPERTY STRINGS EMSCRIPTEN EMDAWNWEBGPU WGPU DAWN)

option(WEBGPU_BUILD_FROM_SOURCE "Build WebGPU from source instead of using precompiled binaries" OFF)

# WebAssembly specific settings
if(EMSCRIPTEN)
	set(CMAKE_CXX_STANDARD 20)
	set(CMAKE_CXX_STANDARD_REQUIRED ON)
	set(CMAKE_CXX_EXTENSIONS OFF)

	# Emscripten specific compile flags
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s USE_WEBGPU=1")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s USE_GLFW=3")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s WASM=1")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s ALLOW_MEMORY_GROWTH=1")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s NO_EXIT_RUNTIME=0")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s ASSERTIONS=1")
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s DISABLE_EXCEPTION_CATCHING=0")

	# Enable WebGPU debugging
	set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s WEBGPU_DEBUG=1")

	# Set output directory for WebAssembly build
	set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/redist_wasm)
endif()

# Fetch WebGPU-distribution
FetchContent_Declare(
	webgpu-distribution
	GIT_REPOSITORY https://github.com/eliemichel/WebGPU-distribution.git
	GIT_TAG main-v0.2.0  # Use a specific tag for stability
)
FetchContent_MakeAvailable(webgpu-distribution)

# Fetch GLFW (only for desktop platforms)
if(NOT EMSCRIPTEN)
	FetchContent_Declare(
		glfw
		GIT_REPOSITORY https://github.com/glfw/glfw.git
		GIT_TAG 3.4
	)
	FetchContent_MakeAvailable(glfw)

	# Fetch glfw3webgpu
	FetchContent_Declare(
		glfw3webgpu
		GIT_REPOSITORY https://github.com/eliemichel/glfw3webgpu.git
		GIT_TAG v1.2.0
	)
	FetchContent_MakeAvailable(glfw3webgpu)
endif()

# Fetch GLM (Mathematics library)
FetchContent_Declare(
	glm
	GIT_REPOSITORY https://github.com/g-truc/glm.git
	GIT_TAG *******
)
FetchContent_MakeAvailable(glm)

# Fetch ImGui
FetchContent_Declare(
	imgui
	GIT_REPOSITORY https://github.com/ocornut/imgui.git
	GIT_TAG v1.90.4
)
FetchContent_MakeAvailable(imgui)

# Create ImGui target
add_library(imgui STATIC
	${imgui_SOURCE_DIR}/imgui.cpp
	${imgui_SOURCE_DIR}/imgui_demo.cpp
	${imgui_SOURCE_DIR}/imgui_draw.cpp
	${imgui_SOURCE_DIR}/imgui_tables.cpp
	${imgui_SOURCE_DIR}/imgui_widgets.cpp
	${imgui_SOURCE_DIR}/backends/imgui_impl_wgpu.cpp
)

target_include_directories(imgui PUBLIC
	${imgui_SOURCE_DIR}
	${imgui_SOURCE_DIR}/backends
)

target_link_libraries(imgui PUBLIC webgpu)

# Add GLFW backend for ImGui on desktop platforms
if(NOT EMSCRIPTEN)
	target_sources(imgui PRIVATE ${imgui_SOURCE_DIR}/backends/imgui_impl_glfw.cpp)
	target_link_libraries(imgui PUBLIC glfw)
endif()

add_executable(App
	main.cpp
	Application.h
	Application.cpp
	ResourceManager.h
	ResourceManager.cpp
	implementations.cpp
)

# Resource directory configuration
if(EMSCRIPTEN)
	# For WebAssembly, embed resources directly
	target_compile_definitions(App PRIVATE
		RESOURCE_DIR="./resources"
	)
	# Preload resource files for better performance
	set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} --preload-file ${CMAKE_CURRENT_SOURCE_DIR}/resources@resources")
elseif(DEV_MODE)
	# In dev mode, we load resources from the source tree, so that when we
	# dynamically edit resources (like shaders), these are correctly
	# versionned.
	target_compile_definitions(App PRIVATE
		RESOURCE_DIR="${CMAKE_CURRENT_SOURCE_DIR}/resources"
	)
else()
	# In release mode, we just load resources relatively to wherever the
	# executable is launched from, so that the binary is portable
	target_compile_definitions(App PRIVATE
		RESOURCE_DIR="./resources"
	)
endif()

target_include_directories(App PRIVATE .)

# Link libraries based on platform
if(EMSCRIPTEN)
	target_link_libraries(App PRIVATE webgpu imgui glm::glm)
else()
	target_link_libraries(App PRIVATE glfw webgpu glfw3webgpu imgui glm::glm)
endif()

# Platform-specific target properties
if(NOT EMSCRIPTEN)
	set_target_properties(App PROPERTIES
		CXX_STANDARD 17
		CXX_STANDARD_REQUIRED ON
		CXX_EXTENSIONS OFF
		VS_DEBUGGER_ENVIRONMENT "DAWN_DEBUG_BREAK_ON_ERROR=1"
	)
	target_treat_all_warnings_as_errors(App)
	target_copy_webgpu_binaries(App)

	# Desktop-specific compile definitions
	target_compile_definitions(App PRIVATE
		GLM_FORCE_DEPTH_ZERO_TO_ONE
		GLM_FORCE_LEFT_HANDED
	)
else()
	set_target_properties(App PROPERTIES
		CXX_STANDARD 20
		CXX_STANDARD_REQUIRED ON
		CXX_EXTENSIONS OFF
		SUFFIX ".html"
	)

	# WebAssembly-specific compile definitions
	target_compile_definitions(App PRIVATE
		GLM_FORCE_DEPTH_ZERO_TO_ONE
		GLM_FORCE_LEFT_HANDED
		WEBGPU_BACKEND_EMSCRIPTEN
	)

	# WebAssembly specific linker flags
	set_target_properties(App PROPERTIES
		LINK_FLAGS "-s USE_WEBGPU=1 -s USE_GLFW=3 -s WASM=1 -s ALLOW_MEMORY_GROWTH=1 -s NO_EXIT_RUNTIME=0 -s ASSERTIONS=1 -s DISABLE_EXCEPTION_CATCHING=0 --shell-file ${CMAKE_CURRENT_SOURCE_DIR}/shell.html"
	)
endif()

# Compiler-specific warnings
if (MSVC)
	# Ignore warnings that GLM and stb_image require to bypass
	target_compile_options(App PUBLIC
		/wd4201  # nonstandard extension used: nameless struct/union
		/wd4305  # truncation from 'int' to 'bool' in 'if' condition
		/wd4244  # conversion from 'int' to 'short', possible loss of data
	)
endif (MSVC)
