@echo off
echo Building WebAssembly version of LearnWebGPU...

REM Set emscripten environment
set EMSDK_PATH=C:\dev\emsdk
call "%EMSDK_PATH%\emsdk_env.bat"

REM Create build directory
if not exist "build_wasm" mkdir build_wasm
cd build_wasm

REM Configure with emscripten
echo Configuring with emscripten...
emcmake cmake .. -DCMAKE_BUILD_TYPE=Release -DWEBGPU_BACKEND=EMSCRIPTEN

REM Build the project
echo Building...
emmake make -j4

REM Create redist directory if it doesn't exist
if not exist "..\redist_wasm" mkdir "..\redist_wasm"

REM Copy built files to redist directory
echo Copying files to redist_wasm...
copy "App.html" "..\redist_wasm\"
copy "App.js" "..\redist_wasm\"
copy "App.wasm" "..\redist_wasm\"
if exist "App.data" copy "App.data" "..\redist_wasm\"

cd ..

echo WebAssembly build completed!
echo Files are in the redist_wasm directory.
echo Open redist_wasm\App.html in a web browser to test.

pause
