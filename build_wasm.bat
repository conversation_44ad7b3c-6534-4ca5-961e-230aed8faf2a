@echo off
echo Building WebAssembly version of LearnWebGPU...

REM Set emscripten environment
set EMSDK_PATH=C:\dev\emsdk
call "%EMSDK_PATH%\emsdk_env.bat"

REM Create build directory
if not exist "build_wasm" mkdir build_wasm
cd build_wasm

REM Choose WebGPU backend for WebAssembly
echo.
echo ========================================
echo Choose WebAssembly WebGPU Backend:
echo 1. EMDAWNWEBGPU (More up-to-date, default)
echo 2. EMSCRIPTEN (Built-in emscripten)
echo ========================================
set /p choice="Enter your choice (1-2): "

if "%choice%"=="2" (
    set BACKEND=EMSCRIPTEN
    echo Using built-in emscripten WebGPU...
) else (
    set BACKEND=EMDAWNWEBGPU
    echo Using emdawnwebgpu backend...
)

REM Configure with emscripten
echo.
echo Configuring with emscripten...
emcmake cmake .. -DCMAKE_BUILD_TYPE=Release -DWEBGPU_BACKEND=%BACKEND%

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    cd ..
    pause
    exit /b 1
)

REM Build the project
echo.
echo Building...
emmake make -j4

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    cd ..
    pause
    exit /b 1
)

REM Create redist directory if it doesn't exist
if not exist "..\redist_wasm" mkdir "..\redist_wasm"

REM Copy built files to redist directory
echo.
echo Copying files to redist_wasm...
copy "App.html" "..\redist_wasm\" 2>nul
copy "App.js" "..\redist_wasm\" 2>nul
copy "App.wasm" "..\redist_wasm\" 2>nul
if exist "App.data" copy "App.data" "..\redist_wasm\" 2>nul

cd ..

echo.
echo ========================================
echo WebAssembly build completed successfully!
echo Backend: %BACKEND%
echo Files are in the redist_wasm directory.
echo Open redist_wasm\App.html in a web browser to test.
echo ========================================
