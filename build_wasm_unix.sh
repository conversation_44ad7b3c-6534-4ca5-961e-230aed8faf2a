#!/bin/bash

echo "Building WebAssembly version of LearnWebGPU..."

# Set emscripten environment
EMSDK_PATH="${EMSDK_PATH:-$HOME/emsdk}"
if [ -f "$EMSDK_PATH/emsdk_env.sh" ]; then
    source "$EMSDK_PATH/emsdk_env.sh"
else
    echo "Error: Emscripten not found at $EMSDK_PATH"
    echo "Please install Emscripten or set EMSDK_PATH environment variable"
    exit 1
fi

# Create build directory
mkdir -p build_wasm
cd build_wasm

# Choose WebGPU backend for WebAssembly
echo ""
echo "========================================"
echo "Choose WebAssembly WebGPU Backend:"
echo "1. EMDAWNWEBGPU (More up-to-date, default)"
echo "2. EMSCRIPTEN (Built-in emscripten)"
echo "========================================"
read -p "Enter your choice (1-2): " choice

if [ "$choice" = "2" ]; then
    BACKEND="EMSCRIPTEN"
    echo "Using built-in emscripten WebGPU..."
else
    BACKEND="EMDAWNWEBGPU"
    echo "Using emdawnwebgpu backend..."
fi

# Configure with emscripten
echo ""
echo "Configuring with emscripten..."
emcmake cmake .. -DCMAKE_BUILD_TYPE=Release -DWEBGPU_BACKEND=$BACKEND

if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    cd ..
    exit 1
fi

# Build the project
echo ""
echo "Building..."
emmake make -j$(nproc 2>/dev/null || sysctl -n hw.ncpu 2>/dev/null || echo 4)

if [ $? -ne 0 ]; then
    echo "Build failed!"
    cd ..
    exit 1
fi

# Create redist directory
mkdir -p ../redist_wasm

# Copy built files to redist directory
echo ""
echo "Copying files to redist_wasm..."
cp App.html ../redist_wasm/ 2>/dev/null
cp App.js ../redist_wasm/ 2>/dev/null
cp App.wasm ../redist_wasm/ 2>/dev/null
[ -f App.data ] && cp App.data ../redist_wasm/ 2>/dev/null

cd ..

echo ""
echo "========================================"
echo "WebAssembly build completed successfully!"
echo "Backend: $BACKEND"
echo "Files are in the redist_wasm directory."
echo "Open redist_wasm/App.html in a web browser to test."
echo "========================================"
