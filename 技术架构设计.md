# LearnWebGPU WebAssembly 技术架构设计

## 1. 项目概述

### 1.1 主要特点
- **跨平台3D渲染**: 支持桌面和Web平台的统一WebGPU渲染
- **现代图形技术**: 使用WebGPU API和WGSL着色器语言
- **高性能Web应用**: 通过WebAssembly实现接近原生的性能
- **实时交互**: 支持鼠标控制和ImGui参数调整
- **资源管理**: 高效的GPU资源管理和RAII封装

### 1.2 技术栈
- **核心语言**: C++20
- **图形API**: WebGPU
- **Web技术**: WebAssembly, HTML5, JavaScript
- **编译工具**: Emscripten, CMake
- **数学库**: GLM (OpenGL Mathematics)
- **窗口管理**: GLFW
- **用户界面**: ImGui

### 1.3 外部依赖
- **WebGPU-distribution**: 统一的WebGPU实现管理
- **Emscripten SDK**: WebAssembly编译工具链
- **WebGPU后端**: wgpu-native/Dawn (桌面), Browser WebGPU/emdawnwebgpu (Web)
- **窗口管理**: GLFW (桌面)
- **数学库**: GLM
- **GUI框架**: Dear ImGui
- **图像处理**: stb_image
- **模型加载**: tiny_obj_loader

## 2. 系统架构

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "Build System"
        CMake[CMake]
        FetchContent[FetchContent]
        Deps[Dependencies]
    end

    subgraph "Web Browser"
        HTML[HTML Shell]
        JS[JavaScript Runtime]
        WASM[WebAssembly Module]
        BrowserWebGPU[Browser WebGPU]
    end

    subgraph "Desktop Platform"
        Desktop[Desktop App]
        GLFW[GLFW Window]
        DesktopWebGPU[WebGPU Backend]
    end

    subgraph "C++ Application"
        App[Application Class]
        RM[ResourceManager]
        Render[Render Pipeline]
        GUI[ImGui Interface]
    end

    subgraph "WebGPU Backends"
        WGPUNative[wgpu-native]
        Dawn[Dawn]
        EmDawn[emdawnwebgpu]
        EmBuiltin[Emscripten WebGPU]
    end

    subgraph "Dependencies (FetchContent)"
        WebGPUDist[WebGPU-distribution]
        GLFWLib[GLFW]
        GLM[GLM Math]
        ImGuiLib[Dear ImGui]
        GLFW3WebGPU[glfw3webgpu]
    end

    CMake --> FetchContent
    FetchContent --> Deps
    Deps --> WebGPUDist
    Deps --> GLFWLib
    Deps --> GLM
    Deps --> ImGuiLib
    Deps --> GLFW3WebGPU

    WebGPUDist --> WGPUNative
    WebGPUDist --> Dawn
    WebGPUDist --> EmDawn
    WebGPUDist --> EmBuiltin

    Desktop --> GLFW
    Desktop --> DesktopWebGPU
    DesktopWebGPU --> WGPUNative
    DesktopWebGPU --> Dawn

    HTML --> JS
    JS --> WASM
    WASM --> BrowserWebGPU
    BrowserWebGPU --> EmDawn
    BrowserWebGPU --> EmBuiltin

    App --> RM
    App --> Render
    App --> GUI
```

### 2.2 数据流

```mermaid
sequenceDiagram
    participant User
    participant HTML
    participant WASM
    participant App
    participant WebGPU
    participant GPU
    
    User->>HTML: Load Page
    HTML->>WASM: Initialize Module
    WASM->>App: Create Application
    App->>WebGPU: Initialize Device
    WebGPU->>GPU: Setup Graphics Context
    
    loop Render Loop
        User->>App: Mouse/Keyboard Input
        App->>App: Update Camera/Uniforms
        App->>WebGPU: Begin Render Pass
        WebGPU->>GPU: Execute Draw Commands
        GPU->>WebGPU: Render Result
        WebGPU->>HTML: Present Frame
        HTML->>User: Display Result
    end
```

## 3. 主要组件详解

### 3.1 类图

```mermaid
classDiagram
    class Application {
        -GLFWwindow* m_window
        -wgpu::Device m_device
        -wgpu::Queue m_queue
        -wgpu::RenderPipeline m_pipeline
        +bool onInit()
        +void onFrame()
        +void onFinish()
        +bool isRunning()
    }
    
    class ResourceManager {
        +static ShaderModule loadShaderModule()
        +static bool loadGeometryFromObj()
        +static Texture loadTexture()
    }
    
    class CameraState {
        +vec2 angles
        +float zoom
    }
    
    class DragState {
        +bool active
        +vec2 startMouse
        +CameraState startCameraState
        +float sensitivity
    }
    
    Application --> ResourceManager : uses
    Application --> CameraState : contains
    Application --> DragState : contains
```

### 3.2 Application类
Application类是整个应用程序的核心，负责：
- **初始化管理**: WebGPU设备、渲染管线、资源加载
- **渲染循环**: 每帧的渲染逻辑和状态更新
- **事件处理**: 鼠标、键盘输入的响应
- **资源管理**: GPU资源的创建和销毁

### 3.3 ResourceManager类
ResourceManager提供静态方法用于资源加载：
- **着色器加载**: 从WGSL文件创建ShaderModule
- **模型加载**: 从OBJ文件解析顶点数据
- **纹理加载**: 从图像文件创建GPU纹理

## 4. 关键数据结构

### 4.1 主要存储结构
```cpp
struct VertexAttributes {
    vec3 position;  // 顶点位置
    vec3 normal;    // 法向量
    vec3 color;     // 顶点颜色
    vec2 uv;        // 纹理坐标
};

struct MyUniforms {
    mat4x4 projectionMatrix;  // 投影矩阵
    mat4x4 viewMatrix;        // 视图矩阵
    mat4x4 modelMatrix;       // 模型矩阵
    vec4 color;               // 基础颜色
    float time;               // 时间参数
};

struct LightingUniforms {
    array<vec4, 2> directions;  // 光源方向
    array<vec4, 2> colors;      // 光源颜色
};
```

### 4.2 内存数据结构
- **顶点缓冲区**: 存储3D模型的顶点数据
- **统一缓冲区**: 存储变换矩阵和渲染参数
- **纹理对象**: 存储2D纹理图像数据
- **深度缓冲区**: 存储深度测试信息

### 4.3 物理存储布局
- **WebAssembly模块**: 包含编译后的C++代码
- **嵌入资源**: 3D模型、纹理、着色器文件
- **GPU内存**: 顶点缓冲区、纹理、渲染目标

## 5. 关键算法和流程

### 5.1 应用初始化流程

```mermaid
flowchart TD
    Start([开始]) --> InitWindow[初始化窗口和设备]
    InitWindow --> InitSurface[初始化渲染表面]
    InitSurface --> InitDepth[初始化深度缓冲]
    InitDepth --> InitPipeline[初始化渲染管线]
    InitPipeline --> LoadShaders[加载着色器]
    LoadShaders --> LoadGeometry[加载3D模型]
    LoadGeometry --> LoadTextures[加载纹理]
    LoadTextures --> InitUniforms[初始化统一缓冲区]
    InitUniforms --> InitGUI[初始化ImGui]
    InitGUI --> Ready([准备就绪])
```

### 5.2 渲染循环流程

```mermaid
flowchart TD
    Frame([开始帧]) --> PollEvents[处理输入事件]
    PollEvents --> UpdateCamera[更新相机状态]
    UpdateCamera --> UpdateUniforms[更新统一缓冲区]
    UpdateUniforms --> BeginRender[开始渲染通道]
    BeginRender --> SetPipeline[设置渲染管线]
    SetPipeline --> BindResources[绑定资源]
    BindResources --> DrawMesh[绘制3D网格]
    DrawMesh --> DrawGUI[绘制ImGui界面]
    DrawGUI --> EndRender[结束渲染通道]
    EndRender --> Present[呈现帧缓冲]
    Present --> NextFrame([下一帧])
```

### 5.3 相机控制算法
- **球坐标系**: 使用角度和距离表示相机位置
- **惯性系统**: 实现平滑的相机移动效果
- **约束处理**: 限制相机的旋转和缩放范围

## 6. 现代构建系统

### 6.1 FetchContent依赖管理
```cmake
include(FetchContent)

# WebGPU-distribution
FetchContent_Declare(
    webgpu-distribution
    GIT_REPOSITORY https://github.com/eliemichel/WebGPU-distribution.git
    GIT_TAG main-v0.2.0
)
FetchContent_MakeAvailable(webgpu-distribution)

# 其他依赖项自动获取
FetchContent_Declare(glfw ...)
FetchContent_Declare(imgui ...)
FetchContent_Declare(glm ...)
```

### 6.2 多后端支持
- **桌面平台**: 自动选择wgpu-native或Dawn
- **Web平台**: 自动选择emdawnwebgpu或内置WebGPU
- **统一接口**: 相同的C++代码适配所有后端

### 6.3 自动化构建脚本
- **Windows**: build_desktop_windows.bat, build_wasm.bat
- **Unix**: build_desktop_unix.sh, build_wasm_unix.sh
- **通用**: build_all.bat/sh 一键构建所有平台

## 7. WebAssembly集成

### 7.1 编译配置
```cmake
if(EMSCRIPTEN)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s USE_WEBGPU=1")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s USE_GLFW=3")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s WASM=1")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -s ALLOW_MEMORY_GROWTH=1")

    # 预加载资源文件
    set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} --preload-file resources@resources")
endif()
```

### 7.2 浏览器接口
- **Canvas元素**: 作为WebGPU渲染目标
- **事件处理**: 鼠标和键盘事件的JavaScript到C++传递
- **模块加载**: WebAssembly模块的异步加载和初始化

## 8. 性能优化

### 7.1 渲染优化
- **批量绘制**: 减少绘制调用次数
- **状态缓存**: 避免重复的GPU状态设置
- **资源复用**: 高效的GPU资源管理

### 7.2 WebAssembly优化
- **编译优化**: 启用-O3优化级别
- **内存管理**: 配置合适的内存增长策略
- **资源预加载**: 减少运行时的文件加载开销

## 9. 扩展性考虑

### 9.1 渲染功能扩展
- **多通道渲染**: 支持阴影映射、后处理效果
- **材质系统**: 支持PBR材质和多纹理
- **动画系统**: 支持骨骼动画和关键帧动画

### 9.2 平台扩展
- **移动端适配**: 支持触摸操作和移动GPU
- **VR/AR支持**: 集成WebXR API
- **多线程渲染**: 利用Web Workers提升性能

### 9.3 构建系统扩展
- **CI/CD集成**: 自动化多平台构建和测试
- **包管理**: 集成vcpkg或Conan包管理器
- **交叉编译**: 支持更多目标平台

## 10. 限制和约束

### 10.1 技术限制
- **WebGPU兼容性**: 需要支持WebGPU的现代浏览器
- **内存限制**: WebAssembly的内存大小限制
- **文件系统**: 无法直接访问本地文件系统

### 10.2 性能约束
- **JavaScript互操作**: C++与JavaScript之间的调用开销
- **网络延迟**: 资源文件的网络加载时间
- **GPU兼容性**: 不同GPU的性能和功能差异

### 10.3 构建约束
- **依赖下载**: 首次构建需要下载大量依赖
- **编译时间**: 从源码构建WebGPU后端耗时较长
- **网络要求**: FetchContent需要稳定的网络连接

## 11. 未来改进方向

### 11.1 技术升级
- **WebGPU新特性**: 跟进WebGPU标准的最新发展
- **编译器优化**: 利用新版本Emscripten的性能改进
- **标准库支持**: 使用更多C++20/23的新特性

### 11.2 功能增强
- **场景管理**: 支持复杂场景的层次结构
- **物理模拟**: 集成物理引擎实现真实的物理效果
- **网络功能**: 支持多用户协作和实时同步

### 11.3 构建系统改进
- **缓存机制**: 实现依赖缓存减少重复下载
- **并行构建**: 优化多平台并行构建流程
- **版本管理**: 更精确的依赖版本控制
